<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
        <PackageReference Include="xunit" Version="2.5.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3"/>
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
        <PackageReference Include="Testcontainers.RabbitMq" Version="4.3.0" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\PubSub\OJS.PubSub.Worker.Models\OJS.PubSub.Worker.Models.csproj" />
      <ProjectReference Include="..\..\..\Servers\UI\OJS.Servers.Ui\OJS.Servers.Ui.csproj" />
      <ProjectReference Include="..\..\..\Servers\Worker\OJS.Servers.Worker\OJS.Servers.Worker.csproj" />
    </ItemGroup>

</Project>
