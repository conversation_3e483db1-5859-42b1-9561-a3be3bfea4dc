# Suppress: EC114
root = true

[*]
# Formatting conventions
end_of_line = lf
insert_final_newline = false
indent_style = space
indent_size = 4
trim_trailing_whitespace = true
max_line_length = 120
charset = utf-8

# language conventions
[*.cs]
dotnet_analyzer_diagnostic.category-Security.severity                    = error
dotnet_analyzer_diagnostic.category-Performance.severity                 = error

dotnet_sort_system_directives_first                                      = false

dotnet_style_qualification_for_field                                     = true : error
dotnet_style_qualification_for_property                                  = true : error
dotnet_style_qualification_for_method                                    = true : error

dotnet_style_require_accessibility_modifiers                             = for_non_interface_members : error
dotnet_style_readonly_field                                              = true : warning

dotnet_style_parentheses_in_arithmetic_binary_operators                  = always_for_clarity : warning
dotnet_style_parentheses_in_relational_binary_operators                  = always_for_clarity : warning
dotnet_style_parentheses_in_other_operators                              = never_if_unnecessary : suggestion

dotnet_style_prefer_conditional_expression_over_assignment               = true : suggestion
dotnet_style_prefer_conditional_expression_over_return                   = true : suggestion
dotnet_style_coalesce_expression                                         = true : suggestion
dotnet_style_null_propagation                                            = true : suggestion

csharp_style_namespace_declarations                                      = file_scoped: suggestion
csharp_style_prefer_primary_constructors                                 = true : suggestion
csharp_using_directive_placement                                         = inside_namespace : warning

csharp_style_var_for_built_in_types                                      = true : suggestion
csharp_style_var_when_type_is_apparent                                   = true : suggestion
csharp_style_var_elsewhere                                               = true : suggestion

csharp_style_expression_bodied_methods                                   = when_on_single_line : warning
csharp_style_expression_bodied_constructors                              = when_on_single_line : warning
csharp_style_expression_bodied_properties                                = when_on_single_line : warning
csharp_style_expression_bodied_accessors                                 = true : warning

csharp_style_pattern_matching_over_as_with_null_check                    = true : suggestion

csharp_style_inlined_variable_declaration                                = true : suggestion

csharp_prefer_simple_default_expression                                  = true : suggestion

csharp_style_throw_expression                                            = false : warning

csharp_prefer_braces                                                     = true : error

csharp_new_line_before_open_brace                                        = all
csharp_new_line_before_else                                              = true
csharp_new_line_before_catch                                             = true
csharp_new_line_before_finally                                           = true
csharp_new_line_before_members_in_object_initializers                    = true
csharp_new_line_before_members_in_anonymous_types                        = true
csharp_new_line_between_query_expression_clauses                         = true

csharp_indent_case_contents                                              = true

csharp_space_after_cast                                                  = false
csharp_space_between_method_declaration_parameter_list_parentheses       = false
csharp_space_between_method_call_empty_parameter_list_parentheses        = false
csharp_space_before_colon_in_inheritance_clause                          = true
csharp_space_after_colon_in_inheritance_clause                           = true
csharp_space_around_binary_operators                                     = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis            = false

csharp_preserve_single_line_statements                                   = false
csharp_preserve_single_line_blocks                                       = true

# Naming conventions

# Style Definitions
dotnet_naming_style.pascal_case_style.capitalization                     = pascal_case
dotnet_naming_style.camel_case_style.capitalization                      = camel_case
dotnet_naming_style.I_prefix_style.required_prefix                       = I
dotnet_naming_style.I_prefix_style.capitalization                        = pascal_case

# Use PascalCase for constant fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.severity        = error
dotnet_naming_rule.constant_fields_should_be_pascal_case.symbols         = constant_fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.style           = pascal_case_style
dotnet_naming_symbols.constant_fields.applicable_kinds                   = field
dotnet_naming_symbols.constant_fields.applicable_accessibilities         = *
dotnet_naming_symbols.constant_fields.required_modifiers                 = const

# Interfaces must be PascalCase and have an I prefix
dotnet_naming_rule.interfaces_start_with_I.severity                      = error
dotnet_naming_rule.interfaces_start_with_I.symbols                       = any_interface
dotnet_naming_rule.interfaces_start_with_I.style                         = I_prefix_style
dotnet_naming_symbols.any_interface.applicable_accessibilities           = *
dotnet_naming_symbols.any_interface.applicable_kinds                     = interface

# Classes, structs, methods, enums, events, properties, namespaces must be PascalCase
dotnet_naming_rule.general_naming.severity                               = error
dotnet_naming_rule.general_naming.symbols                                = general
dotnet_naming_rule.general_naming.style                                  = pascal_case_style
dotnet_naming_symbols.general.applicable_kinds                           = class,struct,enum,property,method,namespace
dotnet_naming_symbols.general.applicable_accessibilities                 = *

# Everything else is camelCase
dotnet_naming_rule.everything_else_naming.severity                       = error
dotnet_naming_rule.everything_else_naming.symbols                        = everything_else
dotnet_naming_rule.everything_else_naming.style                          = camel_case_style
dotnet_naming_symbols.everything_else.applicable_kinds                   = field
dotnet_naming_symbols.everything_else.applicable_accessibilities         = public
