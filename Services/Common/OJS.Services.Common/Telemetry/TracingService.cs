namespace OJS.Services.Common.Telemetry;

using Microsoft.AspNetCore.Http;
using OJS.Servers.Infrastructure.Telemetry;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Claims;
using System.Threading.Tasks;

/// <summary>
/// Unified tracing service implementation for creating and managing activities.
/// Provides a single, standardized way to work with distributed tracing.
/// </summary>
public class TracingService(IHttpContextAccessor httpContextAccessor) : ITracingService
{
    public async Task<T> TraceAsync<T>(
        ActivitySource activitySource,
        string activityName,
        Func<Activity?, Task<T>> operation,
        IDictionary<string, object?>? tags = null,
        BusinessContext? businessContext = null)
    {
        // ActivitySource automatically creates root or child activity based on Activity.Current
        using var activity = activitySource.StartActivity(activityName);

        if (activity != null)
        {
            // Add tags
            if (tags != null)
            {
                foreach (var tag in tags)
                {
                    activity.SetTag(tag.Key, tag.Value);
                }
            }

            // Check if this is a root activity (no parent)
            var isRootActivity = activity.Parent == null;
            if (isRootActivity)
            {
                // Root activity: apply full context enrichment
                this.EnrichWithCommonContext(activity);
                if (businessContext != null)
                {
                    this.AddBusinessContext(activity, businessContext);
                }
            }
            else
            {
                // Child activity: inherit key tags from parent
                InheritParentTags(activity);
            }
        }

        try
        {
            var result = await operation(activity);
            this.MarkAsCompleted(activity);
            return result;
        }
        catch (Exception ex)
        {
            this.MarkAsFailed(activity, ex);
            throw;
        }
    }

    public async Task TraceAsync(
        ActivitySource activitySource,
        string activityName,
        Func<Activity?, Task> operation,
        IDictionary<string, object?>? tags = null,
        BusinessContext? businessContext = null)
    {
        // ActivitySource automatically creates root or child activity based on Activity.Current
        using var activity = activitySource.StartActivity(activityName);

        if (activity != null)
        {
            // Add tags
            if (tags != null)
            {
                foreach (var tag in tags)
                {
                    activity.SetTag(tag.Key, tag.Value);
                }
            }

            // Check if this is a root activity (no parent)
            var isRootActivity = activity.Parent == null;
            if (isRootActivity)
            {
                // Root activity: apply full context enrichment
                this.EnrichWithCommonContext(activity);
                if (businessContext != null)
                {
                    this.AddBusinessContext(activity, businessContext);
                }
            }
            else
            {
                // Child activity: inherit key tags from parent
                InheritParentTags(activity);
            }
        }

        try
        {
            await operation(activity);
            this.MarkAsCompleted(activity);
        }
        catch (Exception ex)
        {
            this.MarkAsFailed(activity, ex);
            throw;
        }
    }

    public void EnrichWithCommonContext(
        Activity activity,
        ClaimsPrincipal? user = null,
        string? correlationId = null)
    {
        // Add service context
        activity.SetTag(OjsActivitySources.CommonTags.ServiceName, GetServiceName());
        activity.SetTag(OjsActivitySources.CommonTags.ServiceVersion, GetServiceVersion());
        activity.SetTag(OjsActivitySources.CommonTags.Environment, GetEnvironment());

        // Add user context if available
        var userPrincipal = user ?? httpContextAccessor.HttpContext?.User;
        if (userPrincipal?.Identity?.IsAuthenticated == true)
        {
            this.AddUserContext(activity, userPrincipal);
        }

        // Add correlation ID - prefer from OpenTelemetry baggage, then parameter, then generate
        var corrId = correlationId ?? this.GetOrGenerateCorrelationId();
        activity.SetTag(OjsActivitySources.CommonTags.CorrelationId, corrId);

        // Also add to baggage for cross-service propagation
        activity.SetBaggage(OjsActivitySources.CommonTags.CorrelationId, corrId);

        // Add request context if available
        var httpContext = httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            activity.SetTag(OjsActivitySources.CommonTags.RequestId, httpContext.TraceIdentifier);

            if (httpContext.Session.IsAvailable)
            {
                activity.SetTag(OjsActivitySources.CommonTags.SessionId, httpContext.Session.Id);
            }
        }
    }

    public void AddUserContext(Activity activity, ClaimsPrincipal user)
    {
        var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = user.FindFirst(ClaimTypes.Name)?.Value ??
                      user.FindFirst("preferred_username")?.Value;
        var userRole = user.FindFirst(ClaimTypes.Role)?.Value;

        if (!string.IsNullOrEmpty(userId))
        {
            activity.SetTag(OjsActivitySources.CommonTags.UserId, userId);
        }

        if (!string.IsNullOrEmpty(userName))
        {
            activity.SetTag(OjsActivitySources.CommonTags.UserName, userName);
        }

        if (!string.IsNullOrEmpty(userRole))
        {
            activity.SetTag(OjsActivitySources.CommonTags.UserRole, userRole);
        }
    }

    public void AddBusinessContext(Activity activity, BusinessContext context)
    {
        if (context.SubmissionId.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.SubmissionId, context.SubmissionId.Value);
        }

        if (context.ProblemId != null)
        {
            activity.SetTag(OjsActivitySources.CommonTags.ProblemId, context.ProblemId);
        }

        if (context.ContestId.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.ContestId, context.ContestId.Value);
        }

        if (context.ParticipantId.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.ParticipantId, context.ParticipantId.Value);
        }

        if (context.CustomTags != null)
        {
            foreach (var tag in context.CustomTags)
            {
                activity.SetTag(tag.Key, tag.Value);
            }
        }
    }

    public void AddTechnicalContext(
        Activity activity,
        string operation,
        string? component = null,
        int? itemCount = null,
        long? dataSize = null)
    {
        activity.SetTag(OjsActivitySources.CommonTags.Operation, operation);

        if (!string.IsNullOrEmpty(component))
        {
            activity.SetTag(OjsActivitySources.CommonTags.Component, component);
        }

        if (itemCount.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.ItemCount, itemCount.Value);
        }

        if (dataSize.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.DataSize, dataSize.Value);
        }
    }

    public void MarkAsCompleted(Activity? activity, string? result = null, TimeSpan? duration = null)
    {
        if (activity == null)
        {
            return;
        }

        activity.SetStatus(ActivityStatusCode.Ok);

        if (!string.IsNullOrEmpty(result))
        {
            activity.SetTag("result", result);
        }

        if (duration.HasValue)
        {
            activity.SetTag(OjsActivitySources.CommonTags.Duration, duration.Value.TotalMilliseconds);
        }
    }

    public void MarkAsFailed(Activity? activity, Exception exception, string? errorType = null)
    {
        if (activity == null)
        {
            return;
        }

        activity.SetStatus(ActivityStatusCode.Error, exception.Message);
        activity.SetTag("error", true);
        activity.SetTag("error.message", exception.Message);
        activity.SetTag("error.type", exception.GetType().Name);

        if (!string.IsNullOrEmpty(errorType))
        {
            activity.SetTag(OjsActivitySources.CommonTags.ErrorType, errorType);
        }

        // Add exception details as an event
        activity.AddEvent(new ActivityEvent("exception", DateTimeOffset.UtcNow, new ActivityTagsCollection
        {
            ["exception.type"] = exception.GetType().FullName,
            ["exception.message"] = exception.Message,
            ["exception.stacktrace"] = exception.StackTrace,
        }));
    }

    public string GetOrGenerateCorrelationId()
    {
        // Try to get correlation ID from current activity
        var currentActivity = Activity.Current;
        if (currentActivity != null)
        {
            var existingCorrelationId = currentActivity.GetTagItem(OjsActivitySources.CommonTags.CorrelationId)?.ToString();
            if (!string.IsNullOrEmpty(existingCorrelationId))
            {
                return existingCorrelationId;
            }
        }

        // Try to get from HTTP context headers
        var httpContext = httpContextAccessor.HttpContext;
        if (httpContext?.Request.Headers.TryGetValue("X-Correlation-ID", out var headerValue) == true)
        {
            var correlationId = headerValue.ToString();
            if (!string.IsNullOrEmpty(correlationId))
            {
                return correlationId;
            }
        }

        // Generate new correlation ID
        return Guid.NewGuid().ToString("N")[..16]; // Short correlation ID
    }

    private static void InheritParentTags(Activity childActivity)
    {
        var parentActivity = childActivity.Parent;
        if (parentActivity == null)
        {
            return;
        }

        var tagsToInherit = new[]
        {
            OjsActivitySources.CommonTags.CorrelationId,
            OjsActivitySources.CommonTags.UserId,
            OjsActivitySources.CommonTags.UserName,
            OjsActivitySources.CommonTags.SubmissionId,
            OjsActivitySources.CommonTags.ProblemId,
            OjsActivitySources.CommonTags.ContestId,
        };

        foreach (var tagName in tagsToInherit)
        {
            var tagValue = parentActivity.GetTagItem(tagName);
            if (tagValue != null)
            {
                childActivity.SetTag(tagName, tagValue);
            }
        }
    }

    private static string GetServiceName()
    {
        var assemblyName = System.Reflection.Assembly.GetEntryAssembly()?.GetName().Name;
        return assemblyName?.Replace("OJS.Servers.", "").Replace("OJS.Services.", "") ?? "Unknown";
    }

    private static string GetServiceVersion()
    {
        var assembly = System.Reflection.Assembly.GetEntryAssembly();
        return assembly?.GetName().Version?.ToString() ?? "1.0.0";
    }

    private static string GetEnvironment()
        => Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
}
