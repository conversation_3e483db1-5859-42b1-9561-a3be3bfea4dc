// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using OJS.Data;

#nullable disable

namespace OJS.Data.Migrations
{
    [DbContext(typeof(OjsDbContext))]
    [Migration("20240912125851_AddedProcessingStateToSubmissionForProcessing")]
    partial class AddedProcessingStateToSubmissionForProcessing
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.DataProtectionKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FriendlyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Xml")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("DataProtectionKeys");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("OJS.Data.Models.AccessLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("PostParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RequestType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AccessLogs");
                });

            modelBuilder.Entity("OJS.Data.Models.Checkers.Checker", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClassName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DllFile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Parameter")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Checkers");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.Contest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowParallelSubmissionsInTasks")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("AutoChangeTestsFeedbackVisibility")
                        .HasColumnType("bit");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("ContestPassword")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan?>("Duration")
                        .HasColumnType("time");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("bit");

                    b.Property<int>("LimitBetweenSubmissions")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NewIpPassword")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<short>("NumberOfProblemGroups")
                        .HasColumnType("smallint");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<DateTime?>("PracticeEndTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PracticePassword")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("PracticeStartTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("VisibleFrom")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("Contests");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ContestCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("ContestCategories");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ExamGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ContestId")
                        .HasColumnType("int");

                    b.Property<string>("ExternalAppId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ExternalExamGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(600)
                        .HasColumnType("nvarchar(600)");

                    b.HasKey("Id");

                    b.HasIndex("ContestId");

                    b.ToTable("ExamGroups");
                });

            modelBuilder.Entity("OJS.Data.Models.FeedbackReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFixed")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("FeedbackReports");
                });

            modelBuilder.Entity("OJS.Data.Models.Ip", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.HasKey("Id");

                    b.HasIndex("Value")
                        .IsUnique();

                    b.ToTable("Ips");
                });

            modelBuilder.Entity("OJS.Data.Models.IpInContest", b =>
                {
                    b.Property<int>("ContestId")
                        .HasColumnType("int");

                    b.Property<int>("IpId")
                        .HasColumnType("int");

                    b.Property<bool>("IsOriginallyAllowed")
                        .HasColumnType("bit");

                    b.HasKey("ContestId", "IpId");

                    b.HasIndex("IpId");

                    b.ToTable("ContestIps");
                });

            modelBuilder.Entity("OJS.Data.Models.LecturerInContest", b =>
                {
                    b.Property<string>("LecturerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ContestId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("LecturerId", "ContestId");

                    b.HasIndex("ContestId");

                    b.ToTable("LecturersInContests");
                });

            modelBuilder.Entity("OJS.Data.Models.LecturerInContestCategory", b =>
                {
                    b.Property<string>("LecturerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ContestCategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("LecturerId", "ContestCategoryId");

                    b.HasIndex("ContestCategoryId");

                    b.ToTable("LecturersInContestCategories");
                });

            modelBuilder.Entity("OJS.Data.Models.Participants.Participant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ContestId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsInvalidated")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOfficial")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ParticipationEndTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ParticipationStartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("TotalScoreSnapshot")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TotalScoreSnapshotModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ContestId");

                    b.HasIndex("UserId");

                    b.ToTable("Participants");
                });

            modelBuilder.Entity("OJS.Data.Models.Participants.ParticipantScore", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsOfficial")
                        .HasColumnType("bit");

                    b.Property<int>("ParticipantId")
                        .HasColumnType("int");

                    b.Property<string>("ParticipantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ParticipantId");

                    b.HasIndex("ProblemId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("ParticipantScores");
                });

            modelBuilder.Entity("OJS.Data.Models.ProblemForParticipant", b =>
                {
                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.Property<int>("ParticipantId")
                        .HasColumnType("int");

                    b.HasKey("ProblemId", "ParticipantId");

                    b.HasIndex("ParticipantId");

                    b.ToTable("ProblemsForParticipants");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.Problem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<byte[]>("AdditionalFiles")
                        .HasColumnType("varbinary(max)");

                    b.Property<int?>("CheckerId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<short>("MaximumPoints")
                        .HasColumnType("smallint");

                    b.Property<int>("MemoryLimit")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<int>("ProblemGroupId")
                        .HasColumnType("int");

                    b.Property<bool>("ShowDetailedFeedback")
                        .HasColumnType("bit");

                    b.Property<bool>("ShowResults")
                        .HasColumnType("bit");

                    b.Property<byte[]>("SolutionSkeleton")
                        .HasColumnType("varbinary(max)");

                    b.Property<int?>("SourceCodeSizeLimit")
                        .HasColumnType("int");

                    b.Property<int>("TimeLimit")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CheckerId");

                    b.HasIndex("ProblemGroupId");

                    b.ToTable("Problems");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.ProblemGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ContestId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<int?>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ContestId");

                    b.ToTable("ProblemGroups");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.ProblemResource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("File")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("FileExtension")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Link")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProblemId");

                    b.ToTable("ProblemResources");
                });

            modelBuilder.Entity("OJS.Data.Models.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Settings");
                });

            modelBuilder.Entity("OJS.Data.Models.SubmissionTypeInProblem", b =>
                {
                    b.Property<int>("SubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.Property<int?>("MemoryLimit")
                        .HasColumnType("int");

                    b.Property<byte[]>("SolutionSkeleton")
                        .HasColumnType("varbinary(max)");

                    b.Property<int?>("TimeLimit")
                        .HasColumnType("int");

                    b.HasKey("SubmissionTypeId", "ProblemId");

                    b.HasIndex("ProblemId");

                    b.ToTable("SubmissionTypeProblems");
                });

            modelBuilder.Entity("OJS.Data.Models.SubmissionTypeInSubmissionDocument", b =>
                {
                    b.Property<int>("SubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionTypeDocumentId")
                        .HasColumnType("int");

                    b.HasKey("SubmissionTypeId", "SubmissionTypeDocumentId");

                    b.HasIndex("SubmissionTypeDocumentId");

                    b.ToTable("SubmissionTypesInSubmissionDocuments");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.Submission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CompilerComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CompletedExecutionOn")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("Content")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileExtension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("varchar");

                    b.Property<bool>("IsCompiledSuccessfully")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsPublic")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<int>("ParticipantId")
                        .HasColumnType("int");

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.Property<bool>("Processed")
                        .HasColumnType("bit");

                    b.Property<string>("ProcessingComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte[]>("SolutionSkeleton")
                        .HasColumnType("varbinary(max)");

                    b.Property<DateTime?>("StartedExecutionOn")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<string>("TestRunsCache")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ParticipantId");

                    b.HasIndex("ProblemId");

                    b.HasIndex("SubmissionTypeId");

                    b.ToTable("Submissions");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.SubmissionForProcessing", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset?>("EnqueuedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset?>("ProcessedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("ProcessingStartedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("State")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId")
                        .IsUnique();

                    b.ToTable("SubmissionsForProcessing");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.SubmissionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalCompilerArguments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("AllowBinaryFilesUpload")
                        .HasColumnType("bit");

                    b.Property<string>("AllowedFileExtensions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BaseMemoryUsedInBytes")
                        .HasColumnType("int");

                    b.Property<int?>("BaseTimeUsedInMilliseconds")
                        .HasColumnType("int");

                    b.Property<int>("CompilerType")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExecutionStrategyType")
                        .HasColumnType("int");

                    b.Property<bool>("IsSelectedByDefault")
                        .HasColumnType("bit");

                    b.Property<int?>("MaxAllowedMemoryLimitInBytes")
                        .HasColumnType("int");

                    b.Property<int?>("MaxAllowedTimeLimitInMilliseconds")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("SubmissionTypes");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.SubmissionTypeDocument", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("SubmissionTypeDocuments");
                });

            modelBuilder.Entity("OJS.Data.Models.Tests.Test", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("HideInput")
                        .HasColumnType("bit");

                    b.Property<byte[]>("InputData")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<bool>("IsOpenTest")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTrialTest")
                        .HasColumnType("bit");

                    b.Property<double>("OrderBy")
                        .HasColumnType("float");

                    b.Property<byte[]>("OutputData")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<int>("ProblemId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProblemId");

                    b.ToTable("Tests");
                });

            modelBuilder.Entity("OJS.Data.Models.Tests.TestRun", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CheckerComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExecutionComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExpectedOutputFragment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsTrialTest")
                        .HasColumnType("bit");

                    b.Property<long>("MemoryUsed")
                        .HasColumnType("bigint");

                    b.Property<int>("ResultType")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<int>("TestId")
                        .HasColumnType("int");

                    b.Property<int>("TimeUsed")
                        .HasColumnType("int");

                    b.Property<string>("UserOutputFragment")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("TestId");

                    b.ToTable("TestRuns");
                });

            modelBuilder.Entity("OJS.Data.Models.UserInExamGroup", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ExamGroupId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "ExamGroupId");

                    b.HasIndex("ExamGroupId");

                    b.ToTable("UsersInExamGroups");
                });

            modelBuilder.Entity("OJS.Data.Models.Users.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("OJS.Data.Models.Users.UserInRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("OJS.Data.Models.Users.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime?>("ModifiedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.UserProfile", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.UserProfile", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.UserProfile", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("OJS.Data.Models.AccessLog", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.Contest", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.ContestCategory", "Category")
                        .WithMany("Contests")
                        .HasForeignKey("CategoryId");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ContestCategory", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.ContestCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ExamGroup", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.Contest", "Contest")
                        .WithMany("ExamGroups")
                        .HasForeignKey("ContestId");

                    b.Navigation("Contest");
                });

            modelBuilder.Entity("OJS.Data.Models.FeedbackReport", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("OJS.Data.Models.IpInContest", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.Contest", "Contest")
                        .WithMany("IpsInContests")
                        .HasForeignKey("ContestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Ip", "Ip")
                        .WithMany("IpsInContests")
                        .HasForeignKey("IpId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contest");

                    b.Navigation("Ip");
                });

            modelBuilder.Entity("OJS.Data.Models.LecturerInContest", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.Contest", "Contest")
                        .WithMany("LecturersInContests")
                        .HasForeignKey("ContestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Users.UserProfile", "Lecturer")
                        .WithMany("LecturersInContests")
                        .HasForeignKey("LecturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contest");

                    b.Navigation("Lecturer");
                });

            modelBuilder.Entity("OJS.Data.Models.LecturerInContestCategory", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.ContestCategory", "ContestCategory")
                        .WithMany("LecturersInContestCategories")
                        .HasForeignKey("ContestCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Users.UserProfile", "Lecturer")
                        .WithMany("LecturersInContestCategories")
                        .HasForeignKey("LecturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContestCategory");

                    b.Navigation("Lecturer");
                });

            modelBuilder.Entity("OJS.Data.Models.Participants.Participant", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.Contest", "Contest")
                        .WithMany("Participants")
                        .HasForeignKey("ContestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Users.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contest");

                    b.Navigation("User");
                });

            modelBuilder.Entity("OJS.Data.Models.Participants.ParticipantScore", b =>
                {
                    b.HasOne("OJS.Data.Models.Participants.Participant", "Participant")
                        .WithMany("Scores")
                        .HasForeignKey("ParticipantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("ParticipantScores")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Submissions.Submission", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId");

                    b.Navigation("Participant");

                    b.Navigation("Problem");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("OJS.Data.Models.ProblemForParticipant", b =>
                {
                    b.HasOne("OJS.Data.Models.Participants.Participant", "Participant")
                        .WithMany("ProblemsForParticipants")
                        .HasForeignKey("ParticipantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("ProblemsForParticipants")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Participant");

                    b.Navigation("Problem");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.Problem", b =>
                {
                    b.HasOne("OJS.Data.Models.Checkers.Checker", "Checker")
                        .WithMany()
                        .HasForeignKey("CheckerId");

                    b.HasOne("OJS.Data.Models.Problems.ProblemGroup", "ProblemGroup")
                        .WithMany("Problems")
                        .HasForeignKey("ProblemGroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Checker");

                    b.Navigation("ProblemGroup");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.ProblemGroup", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.Contest", "Contest")
                        .WithMany("ProblemGroups")
                        .HasForeignKey("ContestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contest");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.ProblemResource", b =>
                {
                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("Resources")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Problem");
                });

            modelBuilder.Entity("OJS.Data.Models.SubmissionTypeInProblem", b =>
                {
                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("SubmissionTypesInProblems")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Submissions.SubmissionType", "SubmissionType")
                        .WithMany("SubmissionTypesInProblems")
                        .HasForeignKey("SubmissionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Problem");

                    b.Navigation("SubmissionType");
                });

            modelBuilder.Entity("OJS.Data.Models.SubmissionTypeInSubmissionDocument", b =>
                {
                    b.HasOne("OJS.Data.Models.Submissions.SubmissionTypeDocument", "SubmissionTypeDocument")
                        .WithMany("SubmissionTypesInSubmissionDocuments")
                        .HasForeignKey("SubmissionTypeDocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Submissions.SubmissionType", "SubmissionType")
                        .WithMany("SubmissionTypesInSubmissionDocuments")
                        .HasForeignKey("SubmissionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubmissionType");

                    b.Navigation("SubmissionTypeDocument");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.Submission", b =>
                {
                    b.HasOne("OJS.Data.Models.Participants.Participant", "Participant")
                        .WithMany("Submissions")
                        .HasForeignKey("ParticipantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("Submissions")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Submissions.SubmissionType", "SubmissionType")
                        .WithMany()
                        .HasForeignKey("SubmissionTypeId");

                    b.Navigation("Participant");

                    b.Navigation("Problem");

                    b.Navigation("SubmissionType");
                });

            modelBuilder.Entity("OJS.Data.Models.Tests.Test", b =>
                {
                    b.HasOne("OJS.Data.Models.Problems.Problem", "Problem")
                        .WithMany("Tests")
                        .HasForeignKey("ProblemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Problem");
                });

            modelBuilder.Entity("OJS.Data.Models.Tests.TestRun", b =>
                {
                    b.HasOne("OJS.Data.Models.Submissions.Submission", "Submission")
                        .WithMany("TestRuns")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Tests.Test", "Test")
                        .WithMany("TestRuns")
                        .HasForeignKey("TestId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Submission");

                    b.Navigation("Test");
                });

            modelBuilder.Entity("OJS.Data.Models.UserInExamGroup", b =>
                {
                    b.HasOne("OJS.Data.Models.Contests.ExamGroup", "ExamGroup")
                        .WithMany("UsersInExamGroups")
                        .HasForeignKey("ExamGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Users.UserProfile", "User")
                        .WithMany("UsersInExamGroups")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExamGroup");

                    b.Navigation("User");
                });

            modelBuilder.Entity("OJS.Data.Models.Users.UserInRole", b =>
                {
                    b.HasOne("OJS.Data.Models.Users.Role", "Role")
                        .WithMany("UsersInRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("OJS.Data.Models.Users.UserProfile", "User")
                        .WithMany("UsersInRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("OJS.Data.Models.Users.UserProfile", b =>
                {
                    b.OwnsOne("OJS.Data.Models.Users.UserSettings", "UserSettings", b1 =>
                        {
                            b1.Property<string>("UserProfileId")
                                .HasColumnType("nvarchar(450)");

                            b1.Property<string>("City")
                                .HasMaxLength(200)
                                .HasColumnType("nvarchar(200)")
                                .HasColumnName("City");

                            b1.Property<string>("Company")
                                .HasMaxLength(200)
                                .HasColumnType("nvarchar(200)")
                                .HasColumnName("Company");

                            b1.Property<DateTime?>("DateOfBirth")
                                .HasColumnType("datetime2")
                                .HasColumnName("DateOfBirth");

                            b1.Property<string>("EducationalInstitution")
                                .HasColumnType("nvarchar(max)")
                                .HasColumnName("EducationalInstitution");

                            b1.Property<string>("FacultyNumber")
                                .HasMaxLength(30)
                                .HasColumnType("nvarchar(30)")
                                .HasColumnName("FacultyNumber");

                            b1.Property<string>("FirstName")
                                .HasMaxLength(30)
                                .HasColumnType("nvarchar(30)")
                                .HasColumnName("FirstName");

                            b1.Property<string>("JobTitle")
                                .HasMaxLength(100)
                                .HasColumnType("nvarchar(100)")
                                .HasColumnName("JobTitle");

                            b1.Property<string>("LastName")
                                .HasMaxLength(30)
                                .HasColumnType("nvarchar(30)")
                                .HasColumnName("LastName");

                            b1.HasKey("UserProfileId");

                            b1.ToTable("AspNetUsers");

                            b1.WithOwner()
                                .HasForeignKey("UserProfileId");
                        });

                    b.Navigation("UserSettings")
                        .IsRequired();
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.Contest", b =>
                {
                    b.Navigation("ExamGroups");

                    b.Navigation("IpsInContests");

                    b.Navigation("LecturersInContests");

                    b.Navigation("Participants");

                    b.Navigation("ProblemGroups");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ContestCategory", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Contests");

                    b.Navigation("LecturersInContestCategories");
                });

            modelBuilder.Entity("OJS.Data.Models.Contests.ExamGroup", b =>
                {
                    b.Navigation("UsersInExamGroups");
                });

            modelBuilder.Entity("OJS.Data.Models.Ip", b =>
                {
                    b.Navigation("IpsInContests");
                });

            modelBuilder.Entity("OJS.Data.Models.Participants.Participant", b =>
                {
                    b.Navigation("ProblemsForParticipants");

                    b.Navigation("Scores");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.Problem", b =>
                {
                    b.Navigation("ParticipantScores");

                    b.Navigation("ProblemsForParticipants");

                    b.Navigation("Resources");

                    b.Navigation("SubmissionTypesInProblems");

                    b.Navigation("Submissions");

                    b.Navigation("Tests");
                });

            modelBuilder.Entity("OJS.Data.Models.Problems.ProblemGroup", b =>
                {
                    b.Navigation("Problems");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.Submission", b =>
                {
                    b.Navigation("TestRuns");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.SubmissionType", b =>
                {
                    b.Navigation("SubmissionTypesInProblems");

                    b.Navigation("SubmissionTypesInSubmissionDocuments");
                });

            modelBuilder.Entity("OJS.Data.Models.Submissions.SubmissionTypeDocument", b =>
                {
                    b.Navigation("SubmissionTypesInSubmissionDocuments");
                });

            modelBuilder.Entity("OJS.Data.Models.Tests.Test", b =>
                {
                    b.Navigation("TestRuns");
                });

            modelBuilder.Entity("OJS.Data.Models.Users.Role", b =>
                {
                    b.Navigation("UsersInRoles");
                });

            modelBuilder.Entity("OJS.Data.Models.Users.UserProfile", b =>
                {
                    b.Navigation("LecturersInContestCategories");

                    b.Navigation("LecturersInContests");

                    b.Navigation("UsersInExamGroups");

                    b.Navigation("UsersInRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
