namespace OJS.Common.Telemetry;

using System.Diagnostics;

public static class ActivitySources
{
    // Activity source names for each service
    public const string UiServiceName = "OJS.Servers.Ui";
    public const string AdministrationServiceName = "OJS.Servers.Administration";
    public const string WorkerServiceName = "OJS.Servers.Worker";
    
    // Shared activity sources
    public static readonly ActivitySource UiActivitySource = new(UiServiceName);
    public static readonly ActivitySource AdministrationActivitySource = new(AdministrationServiceName);
    public static readonly ActivitySource WorkerActivitySource = new(WorkerServiceName);
    
    // Common activity names for cross-service operations
    public static class ActivityNames
    {
        public const string SubmissionProcessing = "submission.processing";
        public const string ProblemEvaluation = "problem.evaluation";
        public const string UserAuthentication = "user.authentication";
        public const string ContestParticipation = "contest.participation";
        public const string AdminOperation = "admin.operation";
        public const string FileUpload = "file.upload";
        public const string DatabaseOperation = "database.operation";
        public const string ExternalApiCall = "external.api.call";
    }
    
    // Dispose all activity sources when application shuts down
    public static void DisposeAll()
    {
        UiActivitySource?.Dispose();
        AdministrationActivitySource?.Dispose();
        WorkerActivitySource?.Dispose();
    }
}
