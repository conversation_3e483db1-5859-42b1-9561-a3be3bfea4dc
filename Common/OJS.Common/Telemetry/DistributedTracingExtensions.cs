namespace OJS.Common.Telemetry;

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

public static class DistributedTracingExtensions
{
    /// <summary>
    /// Adds distributed tracing middleware to the application pipeline
    /// </summary>
    public static IApplicationBuilder UseDistributedTracing(this IApplicationBuilder app, string serviceName)
    {
        return app.UseMiddleware<DistributedTracingMiddleware>(serviceName);
    }

    /// <summary>
    /// Adds distributed tracing middleware with automatic service name detection
    /// </summary>
    public static IApplicationBuilder UseDistributedTracing(this IApplicationBuilder app)
    {
        var serviceName = DetectServiceName();
        return app.UseMiddleware<DistributedTracingMiddleware>(serviceName);
    }

    /// <summary>
    /// Registers cleanup for ActivitySources when the application shuts down
    /// </summary>
    public static IServiceCollection AddDistributedTracingCleanup(this IServiceCollection services)
    {
        services.AddHostedService<DistributedTracingCleanupService>();
        return services;
    }

    private static string DetectServiceName()
    {
        var assemblyName = System.Reflection.Assembly.GetEntryAssembly()?.GetName().Name ?? "Unknown";
        
        return assemblyName switch
        {
            "OJS.Servers.Ui" => ActivitySources.UiServiceName,
            "OJS.Servers.Administration" => ActivitySources.AdministrationServiceName,
            "OJS.Servers.Worker" => ActivitySources.WorkerServiceName,
            _ => assemblyName
        };
    }
}

/// <summary>
/// Background service that cleans up ActivitySources when the application shuts down
/// </summary>
internal class DistributedTracingCleanupService : IHostedService
{
    public Task StartAsync(CancellationToken cancellationToken)
    {
        // Nothing to do on startup
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        // Dispose all activity sources when the application shuts down
        ActivitySources.DisposeAll();
        return Task.CompletedTask;
    }
}
