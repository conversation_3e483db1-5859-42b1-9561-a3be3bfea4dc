# Distributed Tracing Setup for OJS

This document explains how to use the custom ActivitySources for distributed tracing across the UI, Administration, and Worker services.

## Overview

The distributed tracing system allows you to track activities that flow between services, providing end-to-end visibility into request processing.

## Components

### 1. ActivitySources
- `ActivitySources.UiActivitySource` - For UI service activities
- `ActivitySources.AdministrationActivitySource` - For Administration service activities  
- `ActivitySources.WorkerActivitySource` - For Worker service activities

### 2. DistributedTracingService
Helper service with methods to:
- Start activities for specific services
- Execute code within traced activities
- Add context information (user, business entities, correlation IDs)

### 3. DistributedTracingMiddleware
Automatically enriches HTTP requests with additional tracing context.

## Usage Examples

### Basic Activity Creation

```csharp
// Start a simple activity
using var activity = DistributedTracingService.StartUiActivity("user.login");
activity?.SetTag("user.id", userId);

// Your business logic here
await ProcessUserLogin(userId);
```

### Cross-Service Tracing

```csharp
// In UI Service - starting a submission process
using var activity = DistributedTracingService.StartUiActivity(
    ActivitySources.ActivityNames.SubmissionProcessing, 
    ActivityKind.Server);

activity?.SetTag("submission.id", submissionId);
var correlationId = Guid.NewGuid().ToString();
DistributedTracingService.AddCorrelationId(correlationId);

// Call Administration service
var hasPermission = await httpClient.GetAsync($"http://admin-service/api/permissions/{userId}");

// Call Worker service  
var result = await httpClient.PostAsync("http://worker-service/api/evaluate", content);
```

### Using Helper Methods

```csharp
var result = await DistributedTracingService.ExecuteWithActivityAsync(
    ActivitySources.UiServiceName,
    "file.upload",
    async () =>
    {
        DistributedTracingService.AddUserContext(userId);
        DistributedTracingService.AddBusinessContext("file", fileId, "upload");
        
        return await ProcessFileUpload(file);
    },
    ActivityKind.Server,
    new Dictionary<string, object?> 
    {
        ["file.size"] = file.Length,
        ["file.type"] = file.ContentType
    });
```

### Adding Context Information

```csharp
// Add user context
DistributedTracingService.AddUserContext("user123", "john.doe");

// Add business context
DistributedTracingService.AddBusinessContext("submission", "sub456", "evaluate");

// Add correlation ID
DistributedTracingService.AddCorrelationId(correlationId);

// Add custom tags
Activity.Current?.SetTag("custom.property", "value");
```

## Configuration

### 1. Update your Program.cs files

Each service (UI, Administration, Worker) should call:

```csharp
builder.ConfigureOpenTelemetry();
```

### 2. Add the middleware

In your `UseDefaults()` method:

```csharp
app.UseDistributedTracing();
```

### 3. Register cleanup service

In your service configuration:

```csharp
services.AddDistributedTracingCleanup();
```

## Predefined Activity Names

Use the predefined activity names for consistency:

- `ActivitySources.ActivityNames.SubmissionProcessing`
- `ActivitySources.ActivityNames.ProblemEvaluation`
- `ActivitySources.ActivityNames.UserAuthentication`
- `ActivitySources.ActivityNames.ContestParticipation`
- `ActivitySources.ActivityNames.AdminOperation`
- `ActivitySources.ActivityNames.FileUpload`
- `ActivitySources.ActivityNames.DatabaseOperation`
- `ActivitySources.ActivityNames.ExternalApiCall`

## Best Practices

### 1. Use Meaningful Activity Names
```csharp
// Good
using var activity = DistributedTracingService.StartUiActivity("submission.validate.syntax");

// Bad
using var activity = DistributedTracingService.StartUiActivity("process");
```

### 2. Add Relevant Context
```csharp
activity?.SetTag("submission.id", submissionId);
activity?.SetTag("problem.id", problemId);
activity?.SetTag("user.id", userId);
```

### 3. Handle Errors Properly
```csharp
try
{
    await ProcessSubmission();
    activity?.SetStatus(ActivityStatusCode.Ok);
}
catch (Exception ex)
{
    activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
    activity?.SetTag("error.type", ex.GetType().Name);
    throw;
}
```

### 4. Use Correlation IDs for Cross-Service Calls
```csharp
var correlationId = Guid.NewGuid().ToString();
DistributedTracingService.AddCorrelationId(correlationId);

// Pass correlation ID in HTTP headers
httpClient.DefaultRequestHeaders.Add("X-Correlation-ID", correlationId);
```

## Viewing Traces

With your current Grafana Cloud setup, traces will appear in:
1. Grafana Cloud Traces section
2. You can search by trace ID, service name, or custom tags
3. Use the trace view to see the complete flow across services

## Troubleshooting

### Traces Not Appearing
1. Check that `OTEL_EXPORTER_OTLP_ENDPOINT` is set correctly
2. Verify Alloy is forwarding traces to Grafana Cloud
3. Check that activities are being created (use console exporter in development)

### Missing Context
1. Ensure HTTP client instrumentation is enabled
2. Check that correlation IDs are being passed in headers
3. Verify that the middleware is registered early in the pipeline

### Performance Impact
1. Use sampling in production environments
2. Avoid creating too many activities for high-frequency operations
3. Consider using the helper methods for automatic error handling
