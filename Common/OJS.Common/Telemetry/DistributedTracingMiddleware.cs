namespace OJS.Common.Telemetry;

using Microsoft.AspNetCore.Http;
using System.Diagnostics;
using System.Threading.Tasks;

/// <summary>
/// Middleware that automatically creates activities for incoming HTTP requests
/// and enriches them with additional context
/// </summary>
public class DistributedTracingMiddleware
{
    private readonly RequestDelegate next;
    private readonly string serviceName;

    public DistributedTracingMiddleware(RequestDelegate next, string serviceName)
    {
        this.next = next;
        this.serviceName = serviceName;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Get the current activity (created by ASP.NET Core instrumentation)
        var activity = Activity.Current;
        
        if (activity != null)
        {
            // Enrich the activity with additional context
            EnrichActivity(activity, context);
        }

        try
        {
            await next(context);
            
            // Set success status
            activity?.SetStatus(ActivityStatusCode.Ok);
        }
        catch (Exception ex)
        {
            // Set error status and details
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("error.type", ex.GetType().Name);
            activity?.SetTag("error.message", ex.Message);
            activity?.SetTag("error.stack_trace", ex.StackTrace);
            
            throw;
        }
        finally
        {
            // Add response information
            if (activity != null)
            {
                activity.SetTag("http.response.status_code", context.Response.StatusCode);
                activity.SetTag("http.response.size", context.Response.ContentLength);
            }
        }
    }

    private void EnrichActivity(Activity activity, HttpContext context)
    {
        // Add service information
        activity.SetTag("service.name", serviceName);
        activity.SetTag("service.version", GetServiceVersion());

        // Add HTTP request information
        activity.SetTag("http.request.method", context.Request.Method);
        activity.SetTag("http.request.url", GetFullUrl(context.Request));
        activity.SetTag("http.request.user_agent", context.Request.Headers.UserAgent.ToString());
        activity.SetTag("http.request.content_length", context.Request.ContentLength);

        // Add user information if available
        if (context.User.Identity?.IsAuthenticated == true)
        {
            activity.SetTag("user.id", context.User.Identity.Name);
            activity.SetTag("user.authenticated", true);
            
            // Add user roles if available
            var roles = context.User.Claims
                .Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)
                .Select(c => c.Value)
                .ToArray();
            
            if (roles.Length > 0)
            {
                activity.SetTag("user.roles", string.Join(",", roles));
            }
        }
        else
        {
            activity.SetTag("user.authenticated", false);
        }

        // Add correlation ID from headers if present
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId))
        {
            activity.SetTag("correlation.id", correlationId.ToString());
        }

        // Add custom business context based on route
        AddBusinessContext(activity, context);
    }

    private void AddBusinessContext(Activity activity, HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        if (string.IsNullOrEmpty(path))
            return;

        // Extract business entities from common route patterns
        if (path.Contains("/submissions/"))
        {
            var submissionId = ExtractIdFromPath(path, "/submissions/");
            if (!string.IsNullOrEmpty(submissionId))
            {
                activity.SetTag("submission.id", submissionId);
            }
        }
        else if (path.Contains("/problems/"))
        {
            var problemId = ExtractIdFromPath(path, "/problems/");
            if (!string.IsNullOrEmpty(problemId))
            {
                activity.SetTag("problem.id", problemId);
            }
        }
        else if (path.Contains("/contests/"))
        {
            var contestId = ExtractIdFromPath(path, "/contests/");
            if (!string.IsNullOrEmpty(contestId))
            {
                activity.SetTag("contest.id", contestId);
            }
        }
        else if (path.Contains("/users/"))
        {
            var userId = ExtractIdFromPath(path, "/users/");
            if (!string.IsNullOrEmpty(userId))
            {
                activity.SetTag("target.user.id", userId);
            }
        }

        // Add operation type based on HTTP method and path
        var operationType = DetermineOperationType(context.Request.Method, path);
        if (!string.IsNullOrEmpty(operationType))
        {
            activity.SetTag("operation.type", operationType);
        }
    }

    private string? ExtractIdFromPath(string path, string pattern)
    {
        var index = path.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);
        if (index == -1)
            return null;

        var startIndex = index + pattern.Length;
        var endIndex = path.IndexOf('/', startIndex);
        
        if (endIndex == -1)
            endIndex = path.Length;

        if (startIndex >= endIndex)
            return null;

        return path.Substring(startIndex, endIndex - startIndex);
    }

    private string? DetermineOperationType(string method, string path)
    {
        return method.ToUpperInvariant() switch
        {
            "GET" when path.Contains("/api/") => "read",
            "POST" when path.Contains("/api/") => "create",
            "PUT" when path.Contains("/api/") => "update",
            "PATCH" when path.Contains("/api/") => "update",
            "DELETE" when path.Contains("/api/") => "delete",
            "GET" when path.Contains("/health") => "health_check",
            _ => null
        };
    }

    private string GetFullUrl(HttpRequest request)
    {
        return $"{request.Scheme}://{request.Host}{request.PathBase}{request.Path}{request.QueryString}";
    }

    private string GetServiceVersion()
    {
        var assembly = System.Reflection.Assembly.GetEntryAssembly();
        return assembly?.GetName().Version?.ToString() ?? "unknown";
    }
}
