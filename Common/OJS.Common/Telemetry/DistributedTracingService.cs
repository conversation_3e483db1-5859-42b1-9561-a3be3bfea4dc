namespace OJS.Common.Telemetry;

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

public static class DistributedTracingService
{
    /// <summary>
    /// Starts a new activity for the UI service
    /// </summary>
    public static Activity? StartUiActivity(string activityName, ActivityKind kind = ActivityKind.Internal)
        => ActivitySources.UiActivitySource.StartActivity(activityName, kind);

    /// <summary>
    /// Starts a new activity for the Administration service
    /// </summary>
    public static Activity? StartAdministrationActivity(string activityName, ActivityKind kind = ActivityKind.Internal)
        => ActivitySources.AdministrationActivitySource.StartActivity(activityName, kind);

    /// <summary>
    /// Starts a new activity for the Worker service
    /// </summary>
    public static Activity? StartWorkerActivity(string activityName, ActivityKind kind = ActivityKind.Internal)
        => ActivitySources.WorkerActivitySource.StartActivity(activityName, kind);

    /// <summary>
    /// Starts an activity based on the current service context
    /// </summary>
    public static Activity? StartActivity(string serviceName, string activityName, ActivityKind kind = ActivityKind.Internal)
    {
        return serviceName switch
        {
            ActivitySources.UiServiceName => StartUiActivity(activityName, kind),
            ActivitySources.AdministrationServiceName => StartAdministrationActivity(activityName, kind),
            ActivitySources.WorkerServiceName => StartWorkerActivity(activityName, kind),
            _ => throw new ArgumentException($"Unknown service name: {serviceName}")
        };
    }

    /// <summary>
    /// Executes an action within a traced activity
    /// </summary>
    public static void ExecuteWithActivity(
        string serviceName, 
        string activityName, 
        Action action, 
        ActivityKind kind = ActivityKind.Internal,
        Dictionary<string, object?>? tags = null)
    {
        using var activity = StartActivity(serviceName, activityName, kind);
        
        if (activity != null && tags != null)
        {
            foreach (var tag in tags)
            {
                activity.SetTag(tag.Key, tag.Value);
            }
        }

        try
        {
            action();
            activity?.SetStatus(ActivityStatusCode.Ok);
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("error.type", ex.GetType().Name);
            activity?.SetTag("error.message", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Executes an async action within a traced activity
    /// </summary>
    public static async Task ExecuteWithActivityAsync(
        string serviceName, 
        string activityName, 
        Func<Task> action, 
        ActivityKind kind = ActivityKind.Internal,
        Dictionary<string, object?>? tags = null)
    {
        using var activity = StartActivity(serviceName, activityName, kind);
        
        if (activity != null && tags != null)
        {
            foreach (var tag in tags)
            {
                activity.SetTag(tag.Key, tag.Value);
            }
        }

        try
        {
            await action();
            activity?.SetStatus(ActivityStatusCode.Ok);
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("error.type", ex.GetType().Name);
            activity?.SetTag("error.message", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Executes a function within a traced activity and returns the result
    /// </summary>
    public static T ExecuteWithActivity<T>(
        string serviceName, 
        string activityName, 
        Func<T> func, 
        ActivityKind kind = ActivityKind.Internal,
        Dictionary<string, object?>? tags = null)
    {
        using var activity = StartActivity(serviceName, activityName, kind);
        
        if (activity != null && tags != null)
        {
            foreach (var tag in tags)
            {
                activity.SetTag(tag.Key, tag.Value);
            }
        }

        try
        {
            var result = func();
            activity?.SetStatus(ActivityStatusCode.Ok);
            return result;
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("error.type", ex.GetType().Name);
            activity?.SetTag("error.message", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Executes an async function within a traced activity and returns the result
    /// </summary>
    public static async Task<T> ExecuteWithActivityAsync<T>(
        string serviceName, 
        string activityName, 
        Func<Task<T>> func, 
        ActivityKind kind = ActivityKind.Internal,
        Dictionary<string, object?>? tags = null)
    {
        using var activity = StartActivity(serviceName, activityName, kind);
        
        if (activity != null && tags != null)
        {
            foreach (var tag in tags)
            {
                activity.SetTag(tag.Key, tag.Value);
            }
        }

        try
        {
            var result = await func();
            activity?.SetStatus(ActivityStatusCode.Ok);
            return result;
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("error.type", ex.GetType().Name);
            activity?.SetTag("error.message", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Adds correlation ID to the current activity
    /// </summary>
    public static void AddCorrelationId(string correlationId)
    {
        Activity.Current?.SetTag("correlation.id", correlationId);
    }

    /// <summary>
    /// Adds user context to the current activity
    /// </summary>
    public static void AddUserContext(string userId, string? username = null)
    {
        var activity = Activity.Current;
        if (activity != null)
        {
            activity.SetTag("user.id", userId);
            if (!string.IsNullOrEmpty(username))
            {
                activity.SetTag("user.name", username);
            }
        }
    }

    /// <summary>
    /// Adds business context to the current activity
    /// </summary>
    public static void AddBusinessContext(string entityType, string entityId, string? operation = null)
    {
        var activity = Activity.Current;
        if (activity != null)
        {
            activity.SetTag($"{entityType}.id", entityId);
            if (!string.IsNullOrEmpty(operation))
            {
                activity.SetTag("operation", operation);
            }
        }
    }
}
