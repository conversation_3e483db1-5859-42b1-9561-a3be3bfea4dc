# NOTE: Requires **VS2019 16.7** or later

# Style rules with 'Recommended' analysis mode

is_global = true

global_level = -99


# IDE0005: Using directive is unnecessary.
dotnet_diagnostic.IDE0005.severity = warning

# IDE0007: Use implicit type
dotnet_diagnostic.IDE0007.severity = warning

# IDE0008: Use explicit type
dotnet_diagnostic.IDE0008.severity = warning

# IDE0011: Add braces
dotnet_diagnostic.IDE0011.severity = warning

# IDE0016: Use 'throw' expression
dotnet_diagnostic.IDE0016.severity = warning

# IDE0017: Simplify object initialization
dotnet_diagnostic.IDE0017.severity = warning

# IDE0018: Inline variable declaration
dotnet_diagnostic.IDE0018.severity = warning

# IDE0019: Use pattern matching
dotnet_diagnostic.IDE0019.severity = warning

# IDE0020: Use pattern matching
dotnet_diagnostic.IDE0020.severity = warning

# IDE0021: Use expression body for constructor
dotnet_diagnostic.IDE0021.severity = warning

# IDE0022: Use expression body for method
dotnet_diagnostic.IDE0022.severity = warning

# IDE0023: Use expression body for conversion operator
dotnet_diagnostic.IDE0023.severity = warning

# IDE0024: Use expression body for operator
dotnet_diagnostic.IDE0024.severity = warning

# IDE0025: Use expression body for property
dotnet_diagnostic.IDE0025.severity = warning

# IDE0026: Use expression body for indexer
dotnet_diagnostic.IDE0026.severity = warning

# IDE0027: Use expression body for accessor
dotnet_diagnostic.IDE0027.severity = warning

# IDE0028: Simplify collection initialization
dotnet_diagnostic.IDE0028.severity = warning

# IDE0029: Use coalesce expression
dotnet_diagnostic.IDE0029.severity = warning

# IDE0030: Use coalesce expression
dotnet_diagnostic.IDE0030.severity = warning

# IDE0031: Use null propagation
dotnet_diagnostic.IDE0031.severity = warning

# IDE0032: Use auto property
dotnet_diagnostic.IDE0032.severity = warning

# IDE0033: Use explicitly provided tuple name
dotnet_diagnostic.IDE0033.severity = warning

# IDE0034: Simplify 'default' expression
dotnet_diagnostic.IDE0034.severity = warning

# IDE0036: Order modifiers
dotnet_diagnostic.IDE0036.severity = warning

# IDE0039: Use local function
dotnet_diagnostic.IDE0039.severity = warning

# IDE0040: Add accessibility modifiers
dotnet_diagnostic.IDE0040.severity = warning

# IDE0042: Deconstruct variable declaration
dotnet_diagnostic.IDE0042.severity = warning

# IDE0043: Invalid format string
dotnet_diagnostic.IDE0043.severity = warning

# IDE0044: Add readonly modifier
dotnet_diagnostic.IDE0044.severity = warning

# IDE0045: Convert to conditional expression
dotnet_diagnostic.IDE0045.severity = warning

# IDE0046: Convert to conditional expression
dotnet_diagnostic.IDE0046.severity = warning

# IDE0047: Remove unnecessary parentheses
dotnet_diagnostic.IDE0047.severity = warning

# IDE0051: Remove unused private members
dotnet_diagnostic.IDE0051.severity = warning

# IDE0052: Remove unread private members
dotnet_diagnostic.IDE0052.severity = warning

# IDE0053: Use block body for lambda expression
dotnet_diagnostic.IDE0053.severity = warning

# IDE0054: Use compound assignment
dotnet_diagnostic.IDE0054.severity = warning

# IDE0055: Fix formatting
dotnet_diagnostic.IDE0055.severity = warning

# IDE0056: Use index operator
dotnet_diagnostic.IDE0056.severity = warning

# IDE0057: Use range operator
dotnet_diagnostic.IDE0057.severity = warning

# IDE0059: Unnecessary assignment of a value
dotnet_diagnostic.IDE0059.severity = warning

# IDE0060: Remove unused parameter
dotnet_diagnostic.IDE0060.severity = warning

# IDE0061: Use expression body for local function
dotnet_diagnostic.IDE0061.severity = warning

# IDE0062: Make local function 'static'
dotnet_diagnostic.IDE0062.severity = warning

# IDE0063: Use simple 'using' statement
dotnet_diagnostic.IDE0063.severity = warning

# IDE0065: Misplaced using directive
dotnet_diagnostic.IDE0065.severity = warning

# IDE0070: Use 'System.HashCode'
dotnet_diagnostic.IDE0070.severity = warning

# IDE0071: Simplify interpolation
dotnet_diagnostic.IDE0071.severity = warning

# IDE0073: The file header is missing or not located at the top of the file
dotnet_diagnostic.IDE0073.severity = warning

# IDE0074: Use compound assignment
dotnet_diagnostic.IDE0074.severity = warning

# IDE0075: Simplify conditional expression
dotnet_diagnostic.IDE0075.severity = warning

# IDE0076: Invalid global 'SuppressMessageAttribute'
dotnet_diagnostic.IDE0076.severity = warning

# IDE0077: Avoid legacy format target in 'SuppressMessageAttribute'
dotnet_diagnostic.IDE0077.severity = warning

# IDE0078: Use pattern matching
dotnet_diagnostic.IDE0078.severity = warning

# IDE0080: Remove unnecessary suppression operator
dotnet_diagnostic.IDE0080.severity = warning

# IDE0082: 'typeof' can be converted to 'nameof'
dotnet_diagnostic.IDE0082.severity = warning

# IDE0083: Use pattern matching
dotnet_diagnostic.IDE0083.severity = warning

# IDE0090: Use 'new(...)'
dotnet_diagnostic.IDE0090.severity = warning

# IDE0100: Remove redundant equality
dotnet_diagnostic.IDE0100.severity = warning

# IDE0110: Remove unnecessary discard
dotnet_diagnostic.IDE0110.severity = warning

# IDE0130: Namespace does not match folder structure
dotnet_diagnostic.IDE0130.severity = warning

# IDE0160: Convert to block scoped namespace
dotnet_diagnostic.IDE0160.severity = warning

# IDE0161: Convert to file-scoped namespace
dotnet_diagnostic.IDE0161.severity = warning

# IDE0170: Property pattern can be simplified
dotnet_diagnostic.IDE0170.severity = warning

# IDE0180: Use tuple to swap values
dotnet_diagnostic.IDE0180.severity = warning

# IDE0200: Remove unnecessary lambda expression
dotnet_diagnostic.IDE0200.severity = warning

# IDE0240: Remove redundant nullable directive
dotnet_diagnostic.IDE0240.severity = warning

# IDE0241: Remove unnecessary nullable directive
dotnet_diagnostic.IDE0241.severity = warning

# IDE0250: Make struct 'readonly'
dotnet_diagnostic.IDE0250.severity = warning

# IDE0251: Make member 'readonly'
dotnet_diagnostic.IDE0251.severity = warning

# IDE0260: Use pattern matching
dotnet_diagnostic.IDE0260.severity = warning

# IDE0270: Use coalesce expression
dotnet_diagnostic.IDE0270.severity = warning

# IDE0280: Use 'nameof'
dotnet_diagnostic.IDE0280.severity = warning

# IDE0290: Use primary constructor
dotnet_diagnostic.IDE0290.severity = warning

# IDE0300: Simplify collection initialization
dotnet_diagnostic.IDE0300.severity = warning

# IDE0301: Simplify collection initialization
dotnet_diagnostic.IDE0301.severity = warning

# IDE0302: Simplify collection initialization
dotnet_diagnostic.IDE0302.severity = warning

# IDE0303: Simplify collection initialization
dotnet_diagnostic.IDE0303.severity = warning

# IDE0304: Simplify collection initialization
dotnet_diagnostic.IDE0304.severity = warning

# IDE0305: Simplify collection initialization
dotnet_diagnostic.IDE0305.severity = warning

# IDE0306: Simplify collection initialization
dotnet_diagnostic.IDE0306.severity = warning

# IDE0320: Make anonymous function static
dotnet_diagnostic.IDE0320.severity = warning

# IDE0330: Use 'System.Threading.Lock'
dotnet_diagnostic.IDE0330.severity = warning

# IDE0340: Use unbound generic type
dotnet_diagnostic.IDE0340.severity = warning

# IDE1005: Delegate invocation can be simplified.
dotnet_diagnostic.IDE1005.severity = warning

# IDE1006: Naming Styles
dotnet_diagnostic.IDE1006.severity = warning
