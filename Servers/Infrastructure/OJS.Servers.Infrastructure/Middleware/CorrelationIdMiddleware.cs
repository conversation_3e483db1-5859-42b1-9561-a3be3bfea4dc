namespace OJS.Servers.Infrastructure.Middleware;

using Microsoft.AspNetCore.Http;
using OJS.Servers.Infrastructure.Telemetry;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

/// <summary>
/// Middleware to handle correlation ID propagation across HTTP requests.
/// </summary>
public class CorrelationIdMiddleware
{
    private const string CorrelationIdHeaderName = "X-Correlation-ID";
    private readonly RequestDelegate next;

    public CorrelationIdMiddleware(RequestDelegate next)
    {
        this.next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var correlationId = GetOrGenerateCorrelationId(context);
        
        // Set correlation ID in response headers
        context.Response.Headers[CorrelationIdHeaderName] = correlationId;
        
        // Add correlation ID to current activity if one exists
        var currentActivity = Activity.Current;
        if (currentActivity != null)
        {
            currentActivity.SetTag(SubmissionActivitySource.TagNames.CorrelationId, correlationId);
        }

        await this.next(context);
    }

    private static string GetOrGenerateCorrelationId(HttpContext context)
    {
        // Try to get correlation ID from request headers
        if (context.Request.Headers.TryGetValue(CorrelationIdHeaderName, out var headerValue))
        {
            var correlationId = headerValue.ToString();
            if (!string.IsNullOrEmpty(correlationId))
            {
                return correlationId;
            }
        }

        // Generate new correlation ID
        return Guid.NewGuid().ToString("N")[..16]; // Short correlation ID
    }
}
