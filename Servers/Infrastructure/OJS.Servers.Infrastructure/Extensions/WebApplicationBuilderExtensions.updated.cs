namespace OJS.Servers.Infrastructure.Extensions;

using MassTransit.Logging;
using MassTransit.Monitoring;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OJS.Common.Telemetry;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using Serilog.Sinks.OpenTelemetry;
using System.Globalization;

public static class WebApplicationBuilderExtensions
{
    public static WebApplicationBuilder ConfigureOpenTelemetry(this WebApplicationBuilder builder)
    {
        var otlpEndpoint = builder.Configuration["OTEL_EXPORTER_OTLP_ENDPOINT"];
        var environment = builder.Environment;
        var serviceName = GetServiceName(environment);

        // Configure logging
        builder.Host.UseSerilog((hostingContext, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(hostingContext.Configuration)
                .Enrich.FromLogContext()
                .Enrich.WithMachineName()
                .Enrich.WithEnvironmentName()
                .Enrich.WithProperty("service.name", serviceName)
                .WriteTo.Async(wt => wt.Console(formatProvider: CultureInfo.InvariantCulture));

            if (otlpEndpoint != null)
            {
                configuration.WriteTo.OpenTelemetry(options =>
                {
                    options.Endpoint = otlpEndpoint;
                    options.Protocol = OtlpProtocol.Grpc;
                    options.ResourceAttributes = new Dictionary<string, object>
                    {
                        ["service.name"] = serviceName,
                        ["deployment.environment"] = environment.EnvironmentName,
                        ["host.name"] = Environment.MachineName,
                    };
                });
            }
        });

        // Configure metrics and tracing
        var otel = builder.Services.AddOpenTelemetry();

        otel.ConfigureResource(resource =>
            resource
                .AddService(serviceName: serviceName)
                .AddTelemetrySdk()
                .AddEnvironmentVariableDetector()
                .AddAttributes(new Dictionary<string, object>
                {
                    ["deployment.environment"] = environment.EnvironmentName,
                    ["host.name"] = Environment.MachineName,
                }));

        otel.WithMetrics(metrics =>
        {
            metrics
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddRuntimeInstrumentation()
                .AddMeter(InstrumentationOptions.MeterName) // For MassTransit
                .AddMeter(ActivitySources.UiServiceName)
                .AddMeter(ActivitySources.AdministrationServiceName)
                .AddMeter(ActivitySources.WorkerServiceName);
        });

        otel.WithTracing(tracing =>
        {
            if (builder.Environment.IsDevelopment())
            {
                // In development, always sample traces, so we can see them in the console
                tracing.SetSampler<AlwaysOnSampler>();
            }

            tracing
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddEntityFrameworkCoreInstrumentation()
                .AddSource(DiagnosticHeaders.DefaultListenerName) // For MassTransit
                .AddSource(ActivitySources.UiServiceName)
                .AddSource(ActivitySources.AdministrationServiceName)
                .AddSource(ActivitySources.WorkerServiceName);
        });

        if (otlpEndpoint != null)
        {
            // UseOtlpExporter() will use the endpoint from the OTEL_EXPORTER_OTLP_ENDPOINT environment variable
            otel.UseOtlpExporter();
        }

        return builder;
    }

    private static string GetServiceName(IHostEnvironment environment)
    {
        // Determine service name based on the application
        var assemblyName = System.Reflection.Assembly.GetEntryAssembly()?.GetName().Name ?? "Unknown";
        
        return assemblyName switch
        {
            "OJS.Servers.Ui" => ActivitySources.UiServiceName,
            "OJS.Servers.Administration" => ActivitySources.AdministrationServiceName,
            "OJS.Servers.Worker" => ActivitySources.WorkerServiceName,
            _ => assemblyName
        };
    }
}
