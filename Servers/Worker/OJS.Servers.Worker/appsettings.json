{"ApplicationSettings": {"SharedAuthCookieDomain": "localhost", "ApiKey": "12345", "OtlpCollectorBaseUrl": "http://loki:3100", "OtlpCollectorLogsPushPath": "/otlp/v1/logs", "SubmissionsProcessorIdentifierNumber": 1, "UseMessageQueue": true}, "MessageQueue": {"Host": "mq", "VirtualHost": "ojs", "User": "ojsuser", "Password": "myS3cretPass2", "PrefetchCount": 1}, "HealthCheck": {"Key": "Key", "Password": "password"}, "SubmissionExecution": {"OutputResultMaxLength": 1000, "TimeConfig": {"DefaultTimeLimitInMs": 1000, "HtmlAndCssDefaultTimeLimitInMs": 15000}, "MemoryConfig": {"DefaultMemoryLimitInBytes": 16777216}}, "OjsWorkersConfig": {"DotNetCore3RuntimeVersion": "3.1.32", "DotNetCore5RuntimeVersion": "5.0.17", "DotNetCore6RuntimeVersion": "6.0.36", "DotNetCore8RuntimeVersion": "8.0.12", "DotNetCompilerPath": "/usr/bin/dotnet", "DotNetCore3SharedAssembliesPath": "/usr/share/dotnet/shared/Microsoft.NETCore.App/3.1.32", "DotNetCore5SharedAssembliesPath": "/usr/share/dotnet/shared/Microsoft.NETCore.App/5.0.17", "DotNetCore6SharedAssembliesPath": "/usr/lib/dotnet/shared/Microsoft.NETCore.App/6.0.36", "DotNetCore8SharedAssembliesPath": "/usr/lib/dotnet/shared/Microsoft.NETCore.App/8.0.12", "CSharpDotNet3CoreCompilerPath": "/usr/share/dotnet/sdk/3.1.426/Roslyn/bincore/csc.dll", "CSharpDotNetCore5CompilerPath": "/usr/share/dotnet/sdk/5.0.408/Roslyn/bincore/csc.dll", "CSharpDotNetCore6CompilerPath": "/usr/lib/dotnet/sdk/6.0.136/Roslyn/bincore/csc.dll", "CSharpDotNetCore8CompilerPath": "/usr/lib/dotnet/sdk/8.0.112/Roslyn/bincore/csc.dll", "GolangCompilerPath": "/usr/bin/go", "JavaCompilerPath": "/usr/lib/jvm/java-11-openjdk-amd64/bin/javac", "JavaExecutablePath": "/usr/lib/jvm/java-11-openjdk-amd64/bin/java", "Java21CompilerPath": "/usr/lib/jvm/java-1.21.0-openjdk-amd64/bin/javac", "Java21ExecutablePath": "/usr/lib/jvm/java-1.21.0-openjdk-amd64/bin/java", "JavaLibsPath": "/judge-resources/javaLibs/", "Java21LibsPath": "/judge-resources/java21Libs/", "MavenPath": "/usr/bin/mvn", "JavaSpringAndHibernateStrategyPomFilePath": "/judge-resources/java/java-spring-and-hibernate/pom.xml", "Java21SpringAndHibernateStrategyPomFilePath": "/judge-resources/java/java-21-spring-and-hibernate/pom.xml", "NodeJsExecutablePath": "/root/.nvm/versions/node/v12.22.12/bin/node", "NodeJs20ExecutablePath": "/root/.nvm/versions/node/v20.12.0/bin/node", "TypeScriptExecutablePath": "/root/.nvm/versions/node/v20.12.0/bin/tsc", "PythonExecutablePath": "/usr/bin/python3.10", "PhpCliExecutablePath": "/usr/bin/php", "PhpCgiExecutablePath": "/usr/bin/php-cgi", "CPlusPlusGccCompilerPath": "/usr/bin/g++", "NodeResourcesPathPlaceholder": "{nodeResourcesPath}", "NodeResourcesPath": "/judge-resources/js/v12", "Node20ResourcesPath": "/judge-resources/js/v20", "UnderscoreModulePath": "{nodeResourcesPath}/node_modules/underscore", "MochaModulePath": "{nodeResourcesPath}/node_modules/mocha/bin/_mocha", "ChaiModulePath": "{nodeResourcesPath}/node_modules/chai", "PlaywrightModulePath": "{nodeResourcesPath}/node_modules/playwright", "PlaywrightChromiumModulePath": "{nodeResourcesPath}/node_modules/playwright-chromium", "JsDomModulePath": "{nodeResourcesPath}/node_modules/jsdom", "JQueryModulePath": "{nodeResourcesPath}/node_modules/jquery", "HandlebarsModulePath": "{nodeResourcesPath}/node_modules/handlebars", "SinonModulePath": "{nodeResourcesPath}/node_modules/sinon", "SinonJsDomModulePath": "{nodeResourcesPath}/node_modules/sinon/pkg/sinon.js", "SinonChaiModulePath": "{nodeResourcesPath}/node_modules/sinon-chai", "BabelCoreModulePath": "{nodeResourcesPath}/node_modules/babel-core", "ReactJsxPluginPath": "{nodeResourcesPath}/node_modules/babel-plugin-transform-react-jsx", "ReactModulePath": "{nodeResourcesPath}/node_modules/react", "ReactDomModulePath": "{nodeResourcesPath}/node_modules/react-dom", "NodeFetchModulePath": "{nodeResourcesPath}/node_modules/node-fetch", "BootstrapModulePath": "{nodeResourcesPath}/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "BootstrapCssPath": "{nodeResourcesPath}/node_modules/bootstrap/dist/css/bootstrap.min.css", "JsProjNodeModules": "{nodeResourcesPath}/js-run-spa-in-docker-and-execute-mocha-tests/node_modules", "SqlServerLocalDbMasterDbConnectionString": "Data Source=sql_server;User Id=sa; Password=********;TrustServerCertificate=True;", "SqlServerLocalDbRestrictedUserId": "OJS-User", "SqlServerLocalDbRestrictedUserPassword": "********", "MySqlSysDbConnectionString": "Server=mysql;UID=root;Password=********;SslMode=none;", "MySqlRestrictedUserId": "OJS-Restricted", "MySqlRestrictedUserPassword": "123123", "DotNetCliBaseTimeUsedInMilliseconds": 1000, "DotNetCscBaseTimeUsedInMilliseconds": 1000, "DotNetCscBaseMemoryUsedInBytes": 0, "JavaBaseTimeUsedInMilliseconds": 1200, "JavaBaseMemoryUsedInBytes": 90000000, "JavaBaseUpdateTimeOffsetInMilliseconds": 100, "GolangBaseTimeUsedInMilliseconds": 0, "GolangBaseMemoryUsedInBytes": 0, "NodeJsBaseTimeUsedInMilliseconds": 1000, "NodeJsBaseMemoryUsedInBytes": 8388608, "PythonBaseTimeUsedInMilliseconds": 1000, "PythonBaseMemoryUsedInBytes": 0, "PhpCliBaseTimeUsedInMilliseconds": 1000, "PhpCliBaseMemoryUsedInBytes": 0, "DotNetCompilerProcessExitTimeOutMultiplier": 10, "GolangCompilerProcessExitTimeOutMultiplier": 1, "CSharpDotNetCoreCompilerProcessExitTimeOutMultiplier": 1, "JavaCompilerProcessExitTimeOutMultiplier": 1, "JavaInPlaceCompilerProcessExitTimeOutMultiplier": 1, "JavaZipCompilerProcessExitTimeOutMultiplier": 3, "PostgreSqlMasterDbConnectionString": "Server=postgres;UserId=postgres;Password=********;", "PostgreSqlRestrictedUserId": "ojs_user", "PostgreSqlRestrictedUserPassword": "********", "PythonExecutablePathV311": "/root/.pyenv/versions/3.11.5/bin/python", "PythonV311BaseTimeUsedInMilliseconds": 10000, "PythonV311BaseMemoryUsedInBytes": 1024}, "Logging": {"LogLevel": {"Default": "Error", "Microsoft": "Warning"}, "Debug": {"LogLevel": {"Default": "Information", "Microsoft.Hosting": "Trace"}}, "EventSource": {"LogLevel": {"Default": "Warning"}}}, "Http": {"MaxRequestSizeLimit": 35000000}, "CORS": {"AllowedDomains": ["*"]}}