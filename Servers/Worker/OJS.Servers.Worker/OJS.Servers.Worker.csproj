<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileRunEnvironmentFiles>..\..\..\..\Docker\.env.common</DockerfileRunEnvironmentFiles>
    <DockerfileContext>..\..\..\..</DockerfileContext>
    <RootNamespace>OJS.Servers.Worker</RootNamespace>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\PubSub\OJS.PubSub.Worker.Models\OJS.PubSub.Worker.Models.csproj" />
    <ProjectReference Include="..\..\..\Services\Worker\OJS.Services.Worker.Business\OJS.Services.Worker.Business.csproj" />
    <ProjectReference Include="..\..\Infrastructure\OJS.Servers.Infrastructure\OJS.Servers.Infrastructure.csproj" />
  </ItemGroup>
</Project>