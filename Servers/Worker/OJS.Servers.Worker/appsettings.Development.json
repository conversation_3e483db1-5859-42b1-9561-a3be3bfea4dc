{"ApplicationSettings": {"OtlpCollectorBaseUrl": "http://localhost:3100"}, "MessageQueue": {"Host": "localhost"}, "OjsWorkersConfig": {"DotNetCore3RuntimeVersion": "3.1.32", "DotNetCore5RuntimeVersion": "5.0.17", "DotNetCore6RuntimeVersion": "6.0.20", "DotNetCore8RuntimeVersion": "8.0.12", "DotNetCompilerPath": "/usr/bin/dotnet", "DotNetCore3SharedAssembliesPath": "/usr/share/dotnet/shared/Microsoft.NETCore.App/3.1.32", "DotNetCore5SharedAssembliesPath": "/usr/share/dotnet/shared/Microsoft.NETCore.App/5.0.17", "DotNetCore6SharedAssembliesPath": "/usr/share/dotnet/shared/Microsoft.NETCore.App/6.0.36", "DotNetCore8SharedAssembliesPath": "/usr/lib/dotnet/shared/Microsoft.NETCore.App/8.0.12", "CSharpDotNet3CoreCompilerPath": "/usr/share/dotnet/sdk/3.1.426/Roslyn/bincore/csc.dll", "CSharpDotNetCore5CompilerPath": "/usr/share/dotnet/sdk/5.0.408/Roslyn/bincore/csc.dll", "CSharpDotNetCore6CompilerPath": "/usr/share/dotnet/sdk/6.0.412/Roslyn/bincore/csc.dll", "CSharpDotNetCore8CompilerPath": "/usr/lib/dotnet/sdk/8.0.100/Roslyn/bincore/csc.dll", "NodeResourcesPathPlaceholder": "{nodeResourcesPath}", "NodeResourcesPath": "/judge-resources/js/v12", "Node20ResourcesPath": "/judge-resources/js/v20", "NodeJsExecutablePath": "/home/<USER>/.nvm/versions/node/v12.13.0/bin/node", "NodeJs20ExecutablePath": "/root/.nvm/versions/node/v20.12.0/bin/node", "UnderscoreModulePath": "{nodeResourcesPath}/node_modules/underscore", "MochaModulePath": "{nodeResourcesPath}/node_modules/mocha/bin/_mocha", "ChaiModulePath": "{nodeResourcesPath}/node_modules/chai", "JsDomModulePath": "{nodeResourcesPath}/node_modules/jsdom", "JQueryModulePath": "{nodeResourcesPath}/node_modules/jquery", "HandlebarsModulePath": "{nodeResourcesPath}/node_modules/handlebars", "SinonModulePath": "{nodeResourcesPath}/node_modules/sinon", "SinonChaiModulePath": "{nodeResourcesPath}/node_modules/sinon-chai", "BootstrapModulePath": "{nodeResourcesPath}/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js", "BootstrapCssPath": "{nodeResourcesPath}/node_modules/bootstrap/dist/css/bootstrap.min.css", "PlaywrightModulePath": "{nodeResourcesPath}/node_modules/playwright", "PlaywrightChromiumModulePath": "{nodeResourcesPath}/node_modules/playwright-chromium", "JsProjNodeModules": "{nodeResourcesPath}/js-run-spa-in-docker-and-execute-mocha-tests/node_modules", "JavaCompilerPath": "/usr/local/opt/openjdk/bin/javac", "JavaExecutablePath": "/usr/local/opt/openjdk/bin/java", "JavaLibsPath": "/Users/<USER>/repos/interactive/Docker/Judge_Linux/java/javaLibs/", "MavenPath": "/usr/bin/mvn", "PythonExecutablePath": "/usr/local/bin/python3", "PhpCliExecutablePath": "/usr/bin/php", "PhpCgiExecutablePath": "/usr/bin/php-cgi", "CPlusPlusGccCompilerPath": "/usr/bin/g++", "SqlServerLocalDbMasterDbConnectionString": "Data Source=localhost;User Id=sa; Password=********;TrustServerCertificate=True;", "SqlServerLocalDbRestrictedUserId": "OJS-User", "SqlServerLocalDbRestrictedUserPassword": "********", "MySqlSysDbConnectionString": "Server=localhost;UID=root;Password=********;SslMode=none;", "MySqlRestrictedUserId": "OJS-Restricted", "MySqlRestrictedUserPassword": "********", "DotNetCliBaseTimeUsedInMilliseconds": 0, "DotNetCscBaseTimeUsedInMilliseconds": 0, "DotNetCscBaseMemoryUsedInBytes": 0, "JavaBaseTimeUsedInMilliseconds": 0, "JavaBaseMemoryUsedInBytes": 0, "JavaBaseUpdateTimeOffsetInMilliseconds": 0, "NodeJsBaseTimeUsedInMilliseconds": 0, "NodeJsBaseMemoryUsedInBytes": 0, "PythonBaseTimeUsedInMilliseconds": 0, "PythonBaseMemoryUsedInBytes": 0, "PhpCliBaseTimeUsedInMilliseconds": 0, "PhpCliBaseMemoryUsedInBytes": 0, "DotNetCompilerProcessExitTimeOutMultiplier": 1, "CSharpDotNetCoreCompilerProcessExitTimeOutMultiplier": 1, "JavaCompilerProcessExitTimeOutMultiplier": 1}}