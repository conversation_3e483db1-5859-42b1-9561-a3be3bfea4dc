{"ConnectionStrings": {"DefaultConnection": "Data Source=localhost; Initial Catalog=OpenJudgeSystem; Integrated Security=true; TrustServerCertificate=True"}, "Redis": {"ConnectionString": "127.0.0.1,password=redisPass123,ConnectTimeout=10000,abortConnect=false"}, "MessageQueue": {"Host": "localhost"}, "CircuitBreakerResilienceStrategy": {"FailureRatio": 0.5, "MinimumThroughput": 10, "SamplingDuration": "00:00:30", "DurationOfBreak": "00:01:00"}, "RetryResilienceStrategy": {"MaxRetryAttempts": 3, "Delay": "00:00:01", "BackoffType": "Exponential", "UseJitter": true}, "HealthChecksUI": {"HealthChecks": [{"Name": "Administration API", "Uri": "/api/health"}, {"Name": "UI API", "Uri": "http://127.0.0.1:5010/api/health"}, {"Name": "Worker API", "Uri": "http://127.0.0.1:8003/api/health"}], "EvaluationTimeInSeconds": 3600}}