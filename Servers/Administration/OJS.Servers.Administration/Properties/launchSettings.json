{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:12997", "sslPort": 44308}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "OJS.Servers.Administration": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": false, "applicationUrl": "http://localhost:5001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://127.0.0.1:18889", "OTEL_RESOURCE_ATTRIBUTES": "service.name=administration-api, deployment.environment=local"}}}}