import { IAdministrationFilter } from "../../../common/types";

const filterParamsSeparator = '~';

/**
 * Formats a filter object to a string for URL parameters
 * @param filter The filter object to format
 * @returns A string representation of the filter for URL parameters
 */
const formatFilterToString = (filter: IAdministrationFilter) => {
    if (!filter.column || !filter.operator || !filter.value) {
        return;
    }

    // Use field for backend filtering if available, otherwise fall back to columnName
    const filterField = filter.field || filter.column;
    
    // eslint-disable-next-line consistent-return, max-len
    return `${filterField}${filterParamsSeparator}${filter.operator}${filterParamsSeparator}${filter.value}`.toLowerCase();
};

export { formatFilterToString, filterParamsSeparator };
