@use 'src/styles/spacings';
@use 'src/styles/variables';
@use 'src/styles/colors';
@use 'src/styles/font-weights';

.searchPageWrapper {
  margin: 0 auto;
  padding: spacings.$sp-64;
  width: 80vw;

  .searchTextHeader {
    font-size: variables.$f-size-24;
    font-weight: font-weights.$font-weight-medium;
    margin-top: spacings.$sp-16;
    text-align: center;
    word-wrap: break-word;
    max-width: 100%;
    overflow-wrap: break-word;
  }

  .searchSectionHeader {
    font-size: variables.$f-size-24;
    font-weight: font-weights.$font-weight-medium;
    margin-top: spacings.$sp-24;
  }

  .searchResultsWrapper {
    margin-top: spacings.$sp-24;
  }

  .line {
    background-color: #b4b4b4 !important;
    border-color: transparent;
    margin: spacings.$sp-16 0;
  }

  .errorTextWrapper {
    color: colors.$primary-red;
    margin: spacings.$sp-12 0;
  }
}

.spinningLoader {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
}
