@use 'src/styles/components';
@use 'src/styles/spacings';
@use 'src/styles/colors';

.mentor {
  position: fixed;
  bottom: spacings.$sp-16;
  right: spacings.$sp-16;
  z-index: 1000;

  @media (width <= 768px) {
    bottom: spacings.$sp-8;
    right: spacings.$sp-8;
  }
}

.mentorMessageAvatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #fff;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
  }

  @media (width <= 768px) {
    width: 32px;
    height: 32px;
  }

  @media (width <= 480px) {
    width: 28px;
    height: 28px;
  }
}

.mentorTitleContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;

  @media (width <= 768px) {
    gap: 0.75rem;
  }

  @media (width <= 480px) {
    gap: 0.5rem;
  }
}

.closeButtonContainer {
  margin-left: auto;
}

.closeButton {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  padding: 8px !important;
  color: colors.$white-color !important;
  z-index: 10 !important;

  &:hover {
    background-color: rgb(255 255 255 / 10%) !important;
  }

  @media (width <= 768px) {
    min-width: 32px !important;
    min-height: 32px !important;
    padding: 4px !important;
    top: 4px !important;
    right: 4px !important;
  }
}

.titleTextContainer {
  display: flex;
  flex-direction: column;
}

.mentorTitleText {
  font-size: 24px;
  font-weight: bold;
  color: colors.$white-color;

  @media (width <= 768px) {
    font-size: 20px;
  }

  @media (width <= 480px) {
    font-size: 18px;
  }
}

.problemNameText {
  font-size: 16px;
  color: colors.$light-base-color-100;
  font-weight: bold;

  @media (width <= 768px) {
    font-size: 14px;
  }

  @media (width <= 480px) {
    font-size: 12px;
  }
}

.mentorTitleAvatar {
  width: 61px;
  height: 61px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #fff;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
  }

  @media (width <= 768px) {
    width: 48px;
    height: 48px;
  }

  @media (width <= 480px) {
    width: 42px;
    height: 42px;
  }
}

.bubbleMessage {
  position: absolute;
  bottom: calc(100% + 8px);
  right: 0.5rem;
  background-color: #fff;
  padding: spacings.$sp-8 spacings.$sp-16;
  border: colors.$dark-base-color-400 1px solid;
  border-radius: 16px;
  font-size: 18px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  animation: fadeIn 0.3s ease-in;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: colors.$dark-base-color-500;

  .primaryText {
    font-weight: 500;
    margin-bottom: 2px;
  }

  .secondaryText {
    font-size: 16px;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -6px;
    right: 22px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    transform: rotate(45deg);
    box-shadow: 2px 2px 3px rgb(0 0 0 / 5%);
    border-bottom: colors.$dark-base-color-400 1px solid;
    border-right: colors.$dark-base-color-400 1px solid;
  }
}

.errorBubble {
  display: none;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mentorButton {
  position: absolute;
  bottom: 0;
  border-radius: 50% !important;
  width: 90px !important;
  height: 90px !important;
  min-width: 48px !important;
  min-height: 48px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000 !important;
  background-color: colors.$primary-blue !important;
  box-shadow: colors.$box-shadow-color !important;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
  }

  @media (width <= 1200px) {
    width: 80px !important;
    height: 80px !important;
  }

  @media (width <= 1000px) {
    width: 70px !important;
    height: 70px !important;
  }

  @media (width <= 768px) {
    width: 60px !important;
    height: 60px !important;
  }

  @media (width <= 480px) {
    width: 50px !important;
    height: 50px !important;
  }
}

.dialogRoot {
  pointer-events: none;
  z-index: 999 !important;
}

.dialogPaper {
  pointer-events: auto;
  height: 600px;
  width: 690px;
  display: flex;
  flex-direction: column;
  border-radius: spacings.$sp-16 !important;
  font-size: 18px !important;
  position: absolute !important;
  bottom: 7.5rem !important;
  margin: 0 !important;

  @media (height <= 800px) {
    height: 550px;
  }

  @media (height <= 700px) {
    height: 500px;
  }

  @media (height <= 600px) {
    height: 400px;
  }

  @media (height <= 500px) {
    height: 300px !important;
  }

  @media (height <= 400px) {
    height: 200px !important;
  }

  @media (width <= 1200px) {
    height: 550px;
    width: 590px;
    bottom: 7rem !important;
  }

  @media (width <= 1000px) {
    height: 550px;
    width: 590px;
    bottom: 6rem !important;
  }

  @media (width <= 768px) {
    height: 450px;
    width: 480px;
    bottom: 5rem !important;
  }

  @media (width <= 480px) {
    height: 350px;
    width: 100%;
    bottom: 4rem !important;
    right: 0 !important;
  }
}

.dialogTitle {
  background-color: colors.$primary-blue;
  color: colors.$white-color;
  padding: spacings.$sp-16;
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 1rem;
  max-height: 50px;
  font-size: 24px !important;
  position: relative; /* For absolute positioning of close button */

  @media (width <= 768px) {
    font-size: 20px !important;
    padding: spacings.$sp-12;
    max-height: 80px;
  }

  @media (width <= 480px) {
    font-size: 18px !important;
    padding: spacings.$sp-8;
    max-height: 70px;
  }
}

.systemMessageButton {
  background-color: rgb(255 255 255 / 20%) !important;
  color: colors.$white-color !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  min-width: auto !important;
  border-radius: 4px !important;
  text-transform: none !important;
  margin-left: 8px !important;

  &:hover {
    background-color: rgb(255 255 255 / 30%) !important;
  }

  &:disabled {
    color: rgb(255 255 255 / 50%) !important;
  }

  @media (width <= 768px) {
    font-size: 10px !important;
    padding: 2px 6px !important;
  }
}

.dialogContent {
  flex-grow: 1;
  padding: spacings.$sp-16 !important;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  @media (width <= 768px) {
    padding: spacings.$sp-12 !important;
  }

  @media (width <= 480px) {
    padding: spacings.$sp-8 !important;
  }

  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  scrollbar-color: colors.$primary-blue rgba(colors.$primary-blue, 0.1);
  scrollbar-width: thin;
}

.darkDialogContent {
  background-color: colors.$dark-base-color-400;
}

.lightDialogContent {
  background-color: colors.$light-base-color-400;
}

.messagesContainer {
  flex-grow: 1;
  margin-bottom: spacings.$sp-16;

  @media (width <= 480px) {
    margin-bottom: spacings.$sp-8;
  }
}

.messageContainer {
  display: flex;
  align-items: center;
  margin-bottom: spacings.$sp-12;
  gap: 1rem;

  @media (width <= 768px) {
    margin-bottom: spacings.$sp-8;
    gap: 0.75rem;
  }

  @media (width <= 480px) {
    margin-bottom: spacings.$sp-8;
    gap: 0.5rem;
  }
}

.conversationStartDate {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: spacings.$sp-16;
  color: colors.$light-base-color-100;
  font-size: 14px;

  @media (width <= 768px) {
    margin-bottom: spacings.$sp-12;
    font-size: 12px;
  }

  @media (width <= 480px) {
    margin-bottom: spacings.$sp-8;
    font-size: 11px;
  }
}

.message {
  font: inherit;
  padding: spacings.$sp-8 spacings.$sp-12;
  border-radius: 8px;
  max-width: 70%;
  word-break: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.4rem;

  @media (width <= 768px) {
    padding: spacings.$sp-8 spacings.$sp-12;
    line-height: 1.3rem;
    font-size: 16px;
  }

  @media (width <= 480px) {
    padding: spacings.$sp-4 spacings.$sp-8;
    line-height: 1.2rem;
    font-size: 14px;
    max-width: 85%;
  }
}

.userMessage {
  background-color: colors.$dark-base-color-100;
  color: colors.$light-base-color-100;
  margin-left: auto;
}

.mentorMessage {
  background-color: colors.$light-base-color-100;
  color: colors.$dark-base-color-500 !important;
}

.systemMessageContainer {
  width: 100%;
  margin-bottom: spacings.$sp-16;
}

.systemMessage {
  background-color: colors.$dark-base-color-100;
  color: colors.$light-base-color-100 !important;
  width: 100%;
  max-width: 100%;
  padding: spacings.$sp-16;
  border-radius: 8px;
  border-left: 4px solid colors.$primary-blue;

  .markdownContent {
    h1, h2, h3, h4, h5, h6 {
      color: colors.$primary-blue;
    }

    code {
      background-color: colors.$dark-base-color-400;
    }

    pre {
      background-color: colors.$dark-base-color-400;
      padding: spacings.$sp-16;
      margin: spacings.$sp-12 0;
    }

    ul, ol {
      padding-left: spacings.$sp-32;
    }
  }

  @media (width <= 768px) {
    padding: spacings.$sp-12;

    .markdownContent {
      pre {
        padding: spacings.$sp-12;
        margin: spacings.$sp-8 0;
      }
    }
  }

  @media (width <= 480px) {
    padding: spacings.$sp-8;

    .markdownContent {
      pre {
        padding: spacings.$sp-8;
        margin: spacings.$sp-4 0;
      }
    }
  }
}

.dialogActions {
  padding: spacings.$sp-16 !important;
  gap: spacings.$sp-8;
  border-top: 1px solid colors.$wrappers-border-color;
  max-height: 70px;

  @media (width <= 768px) {
    padding: spacings.$sp-12 !important;
    max-height: 65px;
  }

  @media (width <= 480px) {
    padding: spacings.$sp-8 !important;
    max-height: 60px;
    gap: spacings.$sp-4;
  }
}

.typingField {
  & :global(.MuiInput-underline) {
    &::before, &::after {
      border-bottom: none !important;
    }
  }

  & :global(.MuiInput-underline:hover:not(.Mui-disabled))::before {
    border-bottom: none;
  }

  & :global(.MuiInputBase-input) {
    padding: 8px 12px;
    color: colors.$dark-base-color-500 !important;
    border-radius: 4px;
    font-size: 18px;
    max-height: 70px;

    @media (width <= 768px) {
      padding: 6px 10px;
      font-size: 16px;
      max-height: 65px;
    }

    @media (width <= 480px) {
      padding: 4px 8px;
      font-size: 14px;
      max-height: 60px;
    }
  }

  & :global(.MuiInputBase-input::-webkit-scrollbar) {
    display: none;
  }
}

.typingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;

  @media (width <= 768px) {
    gap: 6px;
  }

  @media (width <= 480px) {
    gap: 4px;
  }

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: grow-shrink 1.2s infinite ease-in-out;

    @media (width <= 768px) {
      width: 6px;
      height: 6px;
    }

    @media (width <= 480px) {
      width: 4px;
      height: 4px;
    }
  }

  .dot:nth-child(1) {
    animation-delay: 0s;
  }

  .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  .darkDot {
    background-color: colors.$light-base-color-100;
  }

  .lightDot {
    background-color: colors.$dark-base-color-100;
  }
}

.sendIconActive {
  color: colors.$primary-blue;
  font-size: 28px;

  @media (width <= 768px) {
    font-size: 24px;
  }

  @media (width <= 480px) {
    font-size: 20px;
  }
}

.sendIconDisabled {
  color: rgba(colors.$primary-blue, 0.5);
  font-size: 28px;

  @media (width <= 768px) {
    font-size: 24px;
  }

  @media (width <= 480px) {
    font-size: 20px;
  }
}

@keyframes grow-shrink {
  0%, 100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.5);
  }
}

.errorMessage {
  color: colors.$primary-red;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  bottom: 0;
  font-weight: bold;
}

.markdownContent {
  font-family: inherit;
  line-height: 1.4;
  word-wrap: break-word;
  display: flex;
  flex-direction: column;

  h1, h2, h3, h4, h5, h6 {
    margin-top: spacings.$sp-4;
    margin-bottom: spacings.$sp-4;
    font-weight: bold;
    line-height: 1.4;
  }

  ul, ol {
    padding: 1rem 0 1rem spacings.$sp-24;
    list-style-position: inside;
    display: flex;
    flex-direction: column;
    list-style-type: decimal !important;

    li {
      margin: 0;
      padding-left: spacings.$sp-4;
      line-height: 1.4;

      &::marker {
        font-weight: bold;
      }
    }

    ul, ol {
      padding-left: spacings.$sp-48;
      margin: 0;
      list-style-type: disc !important;
    }
  }

  code {
    background-color: colors.$dark-base-color-300;
    color: colors.$light-base-color-100;
    padding: spacings.$sp-4 spacings.$sp-8;
    border-radius: 4px;
    font-size: 14px;
  }

  pre {
    background-color: colors.$dark-base-color-300;
    color: colors.$light-base-color-100;
    padding: spacings.$sp-12;
    border-radius: 8px;
    overflow-x: auto;
    font-size: 14px;
  }

  a {
    color: colors.$primary-blue;
    text-decoration: underline;

    &:hover {
      color: colors.$primary-blue;
    }
  }

  blockquote {
    border-left: 4px solid colors.$primary-blue;
    padding-left: spacings.$sp-16;
    margin: spacings.$sp-16 0;
    font-style: italic;
    color: colors.$dark-base-color-300;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: spacings.$sp-16 0;

    th, td {
      border: 1px solid colors.$dark-base-color-200;
      padding: spacings.$sp-8;
      text-align: left;
    }

    th {
      background-color: colors.$dark-base-color-100;
      font-weight: bold;
    }

    tr:nth-child(even) {
      background-color: colors.$light-base-color-200;
    }
  }
}

.sendButtonContainer {
  position: relative;
  display: inline-block;

  &:hover .errorBubble {
    display: block;
  }
}
