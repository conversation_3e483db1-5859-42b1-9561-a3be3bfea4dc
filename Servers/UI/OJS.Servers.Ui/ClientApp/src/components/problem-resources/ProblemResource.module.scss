@use 'src/styles/colors';
@use 'src/styles/spacings';

.resourceElement {
  align-items: center;
  color: colors.$secondary-green;
  cursor: pointer;
  display: flex;
  margin-left: spacings.$sp-12;

  svg {
    margin-right: spacings.$sp-4;
  }

  &:hover {
    text-decoration: underline !important;
  }
}

.problemResourceIndicator {
  font-size: 1rem;

  .problemResourceDownloadErrorState {
    color: colors.$primary-red;
    margin-bottom: spacings.$sp-4;
    margin-top: spacings.$sp-4;
  }

  .problemResourceLoading {
    color: colors.$primary-blue;
    margin-top: spacings.$sp-4;
  }
}
