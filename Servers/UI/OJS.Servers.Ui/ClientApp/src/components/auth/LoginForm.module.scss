@use 'src/styles/colors';
@use 'src/styles/spacings';
@use 'src/styles/fonts';
@use 'src/styles/border-radiuses';

.loginFormContentContainer {
  align-content: start;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: spacings.$sp-64;
}

.loginForm {
  @extend %font-family-normal;

  align-content: center;
  border-radius: border-radiuses.$br-6;
  box-shadow: colors.$box-shadow-color;
  display: flex;
  flex-direction: column;
  margin: 20px auto;
  padding: spacings.$sp-48;
  text-align: center;
  width: 400px;

  .logo {
    align-self: center;
    width: fit-content;
  }

  h2 {
    align-self: center;
    border-bottom: 3px solid colors.$primary-blue;
    padding: 0;
  }
}

.loginFormHeader {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.loginFormLink {
  color: colors.$primary-blue;

  &:hover {
    text-decoration: underline;
  }
}

.loginFormControls {
  align-items: center;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  margin: spacings.$sp-16 0;
  width: 100%;
}

.registerHeader {
  margin-top: 50px;
  text-align: center;
}

%loginFormMessage {
  margin-top: 10px;
  text-align: center;
}

.errorMessage {
  @extend %loginFormMessage;

  color: colors.$primary-red;
}

.warningMessage {
  @extend %loginFormMessage;

  color: colors.$warning-color;
}

.loginFormLoader {
  height: 15px;
  margin-bottom: 40px;
  width: 100%;
}
