@use 'src/styles/colors';

.actionsWrapper {
  span {
    align-items: center;
    display: flex;
    justify-content: center;
  }

  .icon {
    cursor: pointer;
    height: 30px;
    vertical-align: middle;
    width: 30px;
  }

  .activeColor {
    color: colors.$primary-blue;
  }

  .dropdownRow {
    align-items: center !important;
    display: flex !important;
    gap: 0.2rem;
  }
}

.dropdown {
  position: absolute;
  top: 0.8rem;

  .dropdownIcon {
    font-size: 30px;
  }
}

.buttons {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  left: 1px;
  margin-left: 0.5rem;
  margin-top: 0.5rem;
  position: absolute;
  top: 1px;
}
