@use 'src/styles/colors';
@use 'src/styles/spacings';
@use 'src/styles/variables';
@use 'src/styles/font-weights';

.errorWrapper {
  align-items: center;
  align-self: center;
  display: flex;
  flex-direction: column;
  font-size: variables.$f-size-20;
  justify-content: center;
  margin-top: spacings.$sp-24;

  .message {
    color: colors.$primary-red;
    font-size: variables.$f-size-24;
    font-weight: font-weights.$font-weight-semi-bold;
  }

  .needHelpWrapper {
    a {
      color: colors.$primary-blue;
    }
  }

  .buttonsWrapper {
    display: flex;
    justify-content: space-evenly;
    margin: spacings.$sp-32;
    width: 100%;
  }
}
