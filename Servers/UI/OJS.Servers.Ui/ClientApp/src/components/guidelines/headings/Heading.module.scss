@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/variables';
@use 'src/styles/font-weights';

.heading {
  @extend %font-family-normal;

  color: colors.$text-dark-gray-color;
  font-weight: font-weights.$font-weight-regular;

  &.primary {
    font-size: variables.$f-size-30;
    line-height: 36px;
    padding: 10px;
  }

  &.secondary {
    font-size: variables.$f-size-24;
    font-weight: font-weights.$font-weight-regular;
    line-height: 30px;
    padding: 10px;

    @media only screen and (max-width: 1024px) {
      font-size: variables.$f-size-24;
      line-height: 34px;
    }
  }

  &.small {
    // TODO: Add styles
  }
}
