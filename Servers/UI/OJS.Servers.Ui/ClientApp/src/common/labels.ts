/* eslint-disable import/group-exports */
/* eslint-disable import/prefer-default-export */
export const ID = 'Id';
export const USER_ID = 'User Id';
export const NAME = 'Name';
export const ORDER_BY = 'Order By';
export const ORDER_BY_DECIMAL = 'Order By (whole or decimal number, e.g. 5 or 5.5)';
export const TYPE = 'Type';
export const DESCRIPTION = 'Description';
export const CATEGORY = 'Category';
export const IS_DELETED = 'Is Deleted';
export const CHECKER = 'Checker';

export const DOWNLOAD = 'Download';

export const TRANSFER = 'Transfer';

export const CREATED_ON = 'Created On';
export const MODIFIED_ON = 'Modified On';

export const USERS = 'Users';

export const EDIT = 'Edit';
export const DELETE = 'Delete';
export const CREATE = 'Create';
export const CANCEL = 'Cancel';
export const PROBLEM_GROUP = 'Problem Group';
export const INPUT = 'Input';
export const OUTPUT = 'Output';
export const PROBLEM_RESOURCE = 'Problem Resource';

export const CREATE_NEW_RECORD = 'Create new record';

export const CLASS_NAME = 'Class Name';

export const VALUE = 'Value';

// Contests
export const LIMIT_BETWEEN_SUBMISSIONS = 'Limit between submissions';
export const AUTO_CHANGE_LIMIT_BETWEEN_SUBMISSIONS = 'Auto change limit between submissions';
export const CONTEST_NAME = 'Contest Name';
export const NUMBER_OF_PROBLEM_GROUPS = 'Number of problem groups';
export const COMPETE_PASSWORD = 'Compete Password';
export const PRACTICE_PASSWORD = 'Practice Password';
export const NEW_IP_PASSWORD = 'New Ip password';
export const ALLOWED_IPS = 'Allowed Ips';
export const DURATION = 'Duration';
export const SELECT_CATEGORY = 'Select Category';
export const SELECT_CONTEST = 'Select Contest';
export const COMPETE_START_TIME = 'Compete Start Time';
export const COMPETE_END_TIME = 'Compete End Time';
export const PRACTICE_START_TIME = 'Practice Start Time';
export const PRACTICE_END_TIME = 'Practice End Time';
export const IS_VISIBLE = 'Is Visible';
export const VISIBLE_FROM = 'Visible From';
export const ALLOW_PARALLEL_SUBMISSIONS_IN_TASKS = 'Allow parallel submissions in tasks';
export const RECORD = 'Record';
export const LINK = 'Link';

// Contest Categories
export const CATEGORY_ID = 'Category Id';

// Problems

export const MAXIMUM_POINTS = 'Maximum Points';
export const SOURCE_CODE_SIZE_LIMIT = 'Source Code Size Limit';
export const MEMORY_LIMIT = 'Memory Limit';
export const TIME_LIMIT = 'Time Limit';
export const PROBLEM_GROUP_TYPE = 'Problem Group Type';
export const SHOW_DETAILED_FEEDBACK = 'Show Detailed feedback';
export const SOLUTION_SKELETON = 'Solution Skeleton';
export const ADDITIONAL_FILES = 'Additional Files';

// Problem groups

export const COPY_INTO_NEW_PROBLEM_GROUP = 'Copy into new problem group';

// Submission types
export const SUBMISSION_TYPE_NAME = 'Submission Type Name';
export const SUBMISSION_TYPE_ID = 'Submission Type Id';
export const SUBMISSION_TYPES = 'Submission Types';
export const EXECUTION_STRATEGY = 'Execution Strategy Type';
export const ALLOWED_FILE_EXTENSIONS = 'Allowed File Extensions';
export const IS_SELECTED = 'Is Selected';
export const ALLOW_BINARY_FILES_UPLOAD = 'Allow Binary files upload';
export const BASE_TIME_USED = 'Base time used in milliseconds';
export const BASE_MEMORY_USED = 'Base memory used in bytes';
export const MAX_ALLOWED_TIME_LIMIT = 'Max allowed time limit in milliseconds';
export const MAX_ALLOWED_MEMORY_LIMIT = 'Max allowed memory limit in bytes';

// Tests
export const TESTS = 'Tests';
export const TEST = 'Test';
export const HIDE_INPUT = 'Hide Input';

// COMPILERS
export const COMPILER = 'Compiler';
export const ADDITIONAL_COMPILER_ARGUMENTS = 'Additional Compiler Arguments';

// Checkers
export const DLL_FILE = 'Dll File';
export const PARAMETER = 'Parameter';

// Participants
export const IS_OFFICIAL = 'Is Official';

// Users
export const JOB_TITLE = 'Job title';
export const LASTNAME = 'LastName';
export const FIRSTNAME = 'FirstName';
export const FACULTY_NUMBER = 'Faculty number';
export const EDUCATIONAL_INSTITUTE = 'Educational institute';
export const DATE_OF_BIRTH = 'Date of birth';
export const COMPANY = 'Company';
export const CITY = 'City';
export const AGE = 'Age';
export const EMAIL = 'Email';
export const USERNAME = 'UserName';

// Submission Type Documents
export const SUBMISSION_TYPE_DOCUMENT = 'Submission type document';
export const SUBMISSION_TYPE_DOCUMENT_ID = 'Submission Type Document Id';
export const SUBMISSION_TYPE_DOCUMENT_TITLE = 'Submission Type Document Title';
export const TITLE = 'Title';

// Users Mentors
export const QUOTA_RESET_TIME = 'Quota Reset Time';
export const REQUESTS_MADE = 'Requests Made';
export const QUOTA_LIMIT = 'Quota Limit';

// Mentor Prompt Templates
export const MENTOR_PROMPT_TEMPLATE_ID = 'Mentor Prompt Template Id';
export const MENTOR_PROMPT_TEMPLATE_TITLE = 'Mentor Prompt Template Title';
export const MENTOR_PROMPT_TEMPLATE = 'Mentor Prompt Template';

// Access Logs
export const IP_ADDRESS = 'IP Address';
export const REQUEST_TYPE = 'Request Type';
export const REQUEST_URL = 'Url';
export const POST_PARAMS = 'Post Params';
