/* eslint-disable import/group-exports */
/* eslint-disable import/prefer-default-export */

// Endpoints
export const CREATE_ENDPOINT = 'Create';
export const UPDATE_ENDPOINT = 'Edit';
export const DELETE_ENDPOINT = 'Delete';
export const GET_ENDPOINT = 'Get';
export const GET_ALL_ENDPOINT = 'GetAll';

export const EXCEL_RESULTS_ENDPOINT = 'GetExcelFile';

// Query Parameters
export const OPEN_CREATE_PARAM = 'openCreate';
export const CATEGORY_ID_PARAM = 'categoryId';

// Paths
export const NEW_ADMINISTRATION_PATH = 'administration';
export const CONTESTS_PATH = 'contests';
export const CONTEST_CATEGORIES_PATH = 'contest-categories';
export const CONTEST_CATEGORIES_HIERARCHY_PATH = 'contest-categories-hierarchy';
export const SUBMISSIONS_PATH = 'submissions';
export const SUBMISSIONS_FOR_PROCESSING_PATH = 'submissions-for-processing';
export const TESTS_PATH = 'tests';
export const PROBLEMS_PATH = 'problems';
export const PROBLEM_GROUPS_PATH = 'problem-groups';
export const PROBLEM_RESOURCES_PATH = 'problem-resources';
export const SUBMISSION_TYPES_PATH = 'submission-types';
export const SUBMISSION_TYPE_DOCUMENTS_PATH = 'submission-type-documents';
export const SUBMISSION_TYPE_DOCUMENTS_VIEW_PATH = 'submission-type-documents-view';
export const USERS_MENTORS_PATH = 'users-mentors';
export const MENTOR_PROMPT_TEMPLATES_PATH = 'mentor-prompt-templates';
export const PARTICIPANTS_PATH = 'participants';
export const CHECKERS_PATH = 'checkers';
export const ROLES_PATH = 'roles';
export const USERS_PATH = 'users';
export const EXAM_GROUPS_PATH = 'exam-groups';
export const SETTINGS_PATH = 'Settings';
export const SUBMISSIONS_SIMILLARITY = 'submissions-simillarity';
export const ACCESS_LOGS_PATH = 'access-logs';
