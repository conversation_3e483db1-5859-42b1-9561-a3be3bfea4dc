@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/resets';

body,
#root {
  @extend %font-family-normal;

  display: flex;
  flex-direction: column;
  margin: 0;
  min-height: 100vh;
  overflow: auto !important;
  padding: 0 !important;
}

.wrapper,
%wrapper {
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  margin: 0 auto;
  min-height: 700px;
  width: 100%;
}

form {
  .btnSubmitInForm {
    margin-top: 10px;
  }
}

/* stylelint-disable */
.columnsThree,
%columnsThree {
  & > * {
    border-left: 1px colors.$bottom-border-color solid;
    padding: 0 15px;

    &:first-child {
      border-left: 0;
    }
  }

  & > *:first-child,
  & > *:last-child {
    flex: 4;
  }

  & > *:nth-child(2) {
    flex: 7;
  }
}

a:link {
  text-decoration: none;

  &:visited {
    text-decoration: none;
  }
}

/* Scrollbar width */
::-webkit-scrollbar {
    height: 10px;
    width: 10px;
}

.lightScrollbar {
    /* Scrollbar Track */
    ::-webkit-scrollbar-track {
        background: colors.$light-base-color-200  // Light theme track color
    }

    /* Scrollbar Handle */
    ::-webkit-scrollbar-thumb {
        background: colors.$light-base-color-500;  // Light theme handle color
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: colors.$primary-blue;
    }
}

.darkScrollbar {
    /* Scrollbar width */
    ::-webkit-scrollbar {
        width: 10px;
        height: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
      background: colors.$primary-dark-grey-color;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
      background: colors.$color-btn-disabled;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
      background: colors.$primary-blue;
    }
}

/* Light Theme Autofill */
.lightAutofill {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
        background-color: colors.$light-base-color-100;
        -webkit-box-shadow: 0 0 0px 1000px colors.$light-base-color-100 inset;
        -webkit-text-fill-color: colors.$light-text-color;
        border: 1px solid colors.$dark-base-color-200;
    }
}

/* Light Theme Autofill */
.darkAutofill {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
        background-color: colors.$dark-base-color-200;
        -webkit-box-shadow: 0 0 0px 1000px colors.$dark-base-color-200 inset;
        -webkit-text-fill-color: colors.$dark-text-color;
        border: 1px solid colors.$light-base-color-100;
    }
}
