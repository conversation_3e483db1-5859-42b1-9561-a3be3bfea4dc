@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/components';
@use 'src/styles/variables';

.content {
  @extend %contentSize;

  align-items: center;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 0 auto;
  padding: 30px 0;
}

.footerLogo {
  max-height: 2vw;
  max-width: 7vw;
}

.systemInfoAndLinksContainer {
  align-content: center;
  align-items: center;
  display: flex;
  flex-direction: row;
  gap: 30px;
  place-content: center;
}

.links {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  gap: 15px;

  svg {
    color: colors.$text-light-gray-color;
    height: 1.2vw;
    width: 1.2vw;

    &:hover {
      color: colors.$white-color;
    }
  }
}
