// vite.config.js
import react from "file:///C:/repos/OpenJudgeSystem/Servers/UI/OJS.Servers.Ui/ClientApp/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { defineConfig } from "file:///C:/repos/OpenJudgeSystem/Servers/UI/OJS.Servers.Ui/ClientApp/node_modules/vite/dist/node/index.js";
import svgr from "file:///C:/repos/OpenJudgeSystem/Servers/UI/OJS.Servers.Ui/ClientApp/node_modules/vite-plugin-svgr/dist/index.js";
import { visualizer } from "file:///C:/repos/OpenJudgeSystem/Servers/UI/OJS.Servers.Ui/ClientApp/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { resolve } from "path";
var __vite_injected_original_dirname = "C:\\repos\\OpenJudgeSystem\\Servers\\UI\\OJS.Servers.Ui\\ClientApp";
var isUnminified = process.env.UNMINIFIED === "true";
var forwardToAdmin = () => {
  return {
    name: "forward-to-admin-html",
    apply: "serve",
    enforce: "post",
    configureServer(server) {
      server.middlewares.use("/", (req, _, next) => {
        if (req.url.startsWith("/administration")) {
          req.url = "/admin.html";
        }
        next();
      });
    }
  };
};
var vite_config_default = defineConfig(({ mode }) => ({
  appType: "mpa",
  build: {
    minify: isUnminified ? false : "esbuild",
    sourcemap: isUnminified,
    rollupOptions: {
      input: {
        main: resolve(__vite_injected_original_dirname, "index.html"),
        admin: resolve(__vite_injected_original_dirname, "admin.html")
      },
      onwarn(warning, warn) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE") {
          return;
        }
        warn(warning);
      }
    }
  },
  plugins: [
    react(),
    svgr(),
    forwardToAdmin(),
    visualizer({ open: true, filename: "bundle-analysis.html" })
  ],
  server: { port: 5002 },
  resolve: {
    alias: {
      "src": resolve(__vite_injected_original_dirname, "src")
    }
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxyZXBvc1xcXFxPcGVuSnVkZ2VTeXN0ZW1cXFxcU2VydmVyc1xcXFxVSVxcXFxPSlMuU2VydmVycy5VaVxcXFxDbGllbnRBcHBcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkM6XFxcXHJlcG9zXFxcXE9wZW5KdWRnZVN5c3RlbVxcXFxTZXJ2ZXJzXFxcXFVJXFxcXE9KUy5TZXJ2ZXJzLlVpXFxcXENsaWVudEFwcFxcXFx2aXRlLmNvbmZpZy5qc1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vQzovcmVwb3MvT3Blbkp1ZGdlU3lzdGVtL1NlcnZlcnMvVUkvT0pTLlNlcnZlcnMuVWkvQ2xpZW50QXBwL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHJlYWN0IGZyb20gJ0B2aXRlanMvcGx1Z2luLXJlYWN0JztcbmltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnO1xuaW1wb3J0IHN2Z3IgZnJvbSAndml0ZS1wbHVnaW4tc3Zncic7XG5pbXBvcnQgeyB2aXN1YWxpemVyIH0gZnJvbSBcInJvbGx1cC1wbHVnaW4tdmlzdWFsaXplclwiO1xuaW1wb3J0IHsgcmVzb2x2ZSB9IGZyb20gJ3BhdGgnO1xuLy8vIDxyZWZlcmVuY2UgdHlwZXM9XCJ2aXRlLXBsdWdpbi1zdmdyL2NsaWVudFwiIC8+XG5cbmNvbnN0IGlzVW5taW5pZmllZCA9IHByb2Nlc3MuZW52LlVOTUlOSUZJRUQgPT09ICd0cnVlJztcblxuLy8gRm9yIGRldmVsb3BtZW50IHNlcnZlciwgd2Ugd2FudCB0byBmb3J3YXJkIGFsbCByZXF1ZXN0cyB0byAvYWRtaW5pc3RyYXRpb24gdG8gL2FkbWluLmh0bWxcbmNvbnN0IGZvcndhcmRUb0FkbWluID0gKCkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIG5hbWU6ICdmb3J3YXJkLXRvLWFkbWluLWh0bWwnLFxuICAgICAgICBhcHBseTogJ3NlcnZlJyxcbiAgICAgICAgZW5mb3JjZTogJ3Bvc3QnLFxuICAgICAgICBjb25maWd1cmVTZXJ2ZXIoc2VydmVyKSB7XG4gICAgICAgICAgICBzZXJ2ZXIubWlkZGxld2FyZXMudXNlKCcvJywgKHJlcSwgXywgbmV4dCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChyZXEudXJsLnN0YXJ0c1dpdGgoJy9hZG1pbmlzdHJhdGlvbicpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlcS51cmwgPSAnL2FkbWluLmh0bWwnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBuZXh0KClcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0sXG4gICAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoKHsgbW9kZSB9KSA9PlxuICAgICh7XG4gICAgICAgIGFwcFR5cGU6ICdtcGEnLCAvLyBNdWx0aSBQYWdlIEFwcGxpY2F0aW9uXG4gICAgICAgIGJ1aWxkOiB7XG4gICAgICAgICAgICBtaW5pZnk6IGlzVW5taW5pZmllZCA/IGZhbHNlIDogJ2VzYnVpbGQnLFxuICAgICAgICAgICAgc291cmNlbWFwOiBpc1VubWluaWZpZWQsXG4gICAgICAgICAgICByb2xsdXBPcHRpb25zOiB7XG4gICAgICAgICAgICAgICAgaW5wdXQ6IHtcbiAgICAgICAgICAgICAgICAgICAgbWFpbjogcmVzb2x2ZShfX2Rpcm5hbWUsICdpbmRleC5odG1sJyksXG4gICAgICAgICAgICAgICAgICAgIGFkbWluOiByZXNvbHZlKF9fZGlybmFtZSwgJ2FkbWluLmh0bWwnKVxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgb253YXJuKHdhcm5pbmcsIHdhcm4pIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHdhcm5pbmcuY29kZSA9PT0gJ01PRFVMRV9MRVZFTF9ESVJFQ1RJVkUnKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB3YXJuKHdhcm5pbmcpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgcGx1Z2luczogW1xuICAgICAgICAgICAgcmVhY3QoKSxcbiAgICAgICAgICAgIHN2Z3IoKSxcbiAgICAgICAgICAgIGZvcndhcmRUb0FkbWluKCksXG4gICAgICAgICAgICB2aXN1YWxpemVyKHtvcGVuOiB0cnVlLCBmaWxlbmFtZTogJ2J1bmRsZS1hbmFseXNpcy5odG1sJ30pLFxuICAgICAgICBdLFxuICAgICAgICBzZXJ2ZXI6IHtwb3J0OiA1MDAyfSxcbiAgICAgICAgcmVzb2x2ZToge1xuICAgICAgICAgICAgYWxpYXM6IHtcbiAgICAgICAgICAgICAgICAnc3JjJzogcmVzb2x2ZShfX2Rpcm5hbWUsICdzcmMnKSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgfSkpO1xuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUFvWCxPQUFPLFdBQVc7QUFDdFksU0FBUyxvQkFBb0I7QUFDN0IsT0FBTyxVQUFVO0FBQ2pCLFNBQVMsa0JBQWtCO0FBQzNCLFNBQVMsZUFBZTtBQUp4QixJQUFNLG1DQUFtQztBQU96QyxJQUFNLGVBQWUsUUFBUSxJQUFJLGVBQWU7QUFHaEQsSUFBTSxpQkFBaUIsTUFBTTtBQUN6QixTQUFPO0FBQUEsSUFDSCxNQUFNO0FBQUEsSUFDTixPQUFPO0FBQUEsSUFDUCxTQUFTO0FBQUEsSUFDVCxnQkFBZ0IsUUFBUTtBQUNwQixhQUFPLFlBQVksSUFBSSxLQUFLLENBQUMsS0FBSyxHQUFHLFNBQVM7QUFDMUMsWUFBSSxJQUFJLElBQUksV0FBVyxpQkFBaUIsR0FBRztBQUN2QyxjQUFJLE1BQU07QUFBQSxRQUNkO0FBQ0EsYUFBSztBQUFBLE1BQ1QsQ0FBQztBQUFBLElBQ0w7QUFBQSxFQUNKO0FBQ0o7QUFFQSxJQUFPLHNCQUFRLGFBQWEsQ0FBQyxFQUFFLEtBQUssT0FDL0I7QUFBQSxFQUNHLFNBQVM7QUFBQSxFQUNULE9BQU87QUFBQSxJQUNILFFBQVEsZUFBZSxRQUFRO0FBQUEsSUFDL0IsV0FBVztBQUFBLElBQ1gsZUFBZTtBQUFBLE1BQ1gsT0FBTztBQUFBLFFBQ0gsTUFBTSxRQUFRLGtDQUFXLFlBQVk7QUFBQSxRQUNyQyxPQUFPLFFBQVEsa0NBQVcsWUFBWTtBQUFBLE1BQzFDO0FBQUEsTUFDQSxPQUFPLFNBQVMsTUFBTTtBQUNsQixZQUFJLFFBQVEsU0FBUywwQkFBMEI7QUFDM0M7QUFBQSxRQUNKO0FBQ0EsYUFBSyxPQUFPO0FBQUEsTUFDaEI7QUFBQSxJQUNKO0FBQUEsRUFDSjtBQUFBLEVBQ0EsU0FBUztBQUFBLElBQ0wsTUFBTTtBQUFBLElBQ04sS0FBSztBQUFBLElBQ0wsZUFBZTtBQUFBLElBQ2YsV0FBVyxFQUFDLE1BQU0sTUFBTSxVQUFVLHVCQUFzQixDQUFDO0FBQUEsRUFDN0Q7QUFBQSxFQUNBLFFBQVEsRUFBQyxNQUFNLEtBQUk7QUFBQSxFQUNuQixTQUFTO0FBQUEsSUFDTCxPQUFPO0FBQUEsTUFDSCxPQUFPLFFBQVEsa0NBQVcsS0FBSztBQUFBLElBQ25DO0FBQUEsRUFDSjtBQUNKLEVBQUU7IiwKICAibmFtZXMiOiBbXQp9Cg==
