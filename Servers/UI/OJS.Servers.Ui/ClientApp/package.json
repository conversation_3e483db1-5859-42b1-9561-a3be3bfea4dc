{"name": "judge.servers.ui", "version": "0.1.0", "private": true, "homepage": "https://localhost:5002", "type": "module", "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@material-ui/core": "^4.12.3", "@monaco-editor/react": "4.6.0", "@mui/base": "^5.0.0-beta.58", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.21", "@mui/system": "^5.2.4", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.19.4", "@mui/x-tree-view": "^7.8.0", "@reduxjs/toolkit": "^1.9.7", "@types/prismjs": "^1.16.6", "@vitejs/plugin-react": "^2.0.1", "date-fns": "^2.25.0", "dayjs": "^1.11.10", "lodash": "^4.17.21", "moment": "^2.29.2", "monaco-editor": "^0.34.1", "prismjs": "^1.25.0", "quill": "^2.0.2", "react": "^18.3.1", "react-alice-carousel": "^2.5.1", "react-arborist": "^3.4.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-icons": "^4.3.1", "react-json-view": "^1.21.3", "react-lite-youtube-embed": "^2.4.0", "react-markdown": "^8.0.7", "react-redux": "^8.1.3", "react-router": "^6.24.0", "react-router-dom": "^6.24.0", "redux-persist": "^6.0.0", "redux-persist-indexeddb-storage": "^1.0.4", "sass": "^1.69.5", "use-resize-observer": "^9.1.0", "uuid": "^9.0.0", "vite": "^3.0.7", "yarn": "^1.22.17"}, "devDependencies": {"@types/lodash": "^4.14.175", "@types/node": "18.11.2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/uuid": "^8.3.1", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-css-modules": "^2.11.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-simple-import-sort": "^8.0.0", "postcss": "^8.4.14", "postcss-scss": "^4.0.4", "rollup-plugin-visualizer": "^5.12.0", "scss": "^0.2.4", "stylelint": "^14.13.0", "stylelint-config-sass-guidelines": "^9.0.1", "stylelint-config-standard": "^29.0.0", "stylelint-config-standard-scss": "^5.0.0", "typescript": "^5.3.3", "vite-plugin-svgr": "^4.1.0"}, "scripts": {"start": "vite", "build": "vite build", "build:unminified": "cross-env UNMINIFIED=true vite build", "build:staging": "vite build --mode staging", "build:staging:unminified": "cross-env UNMINIFIED=true vite build --mode staging", "build:local": "vite build --mode development.local", "lint:eslint": "./node_modules/.bin/eslint ./src --ext .tsx --ext .ts", "lint:stylelint": "./node_modules/.bin/stylelint ./src/**/*.scss", "lint": "yarn run lint:eslint && yarn run lint:stylelint", "analyze": "source-map-explorer 'build/static/js/*.js'"}, "engines": {"node": ">=10.0.0"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}