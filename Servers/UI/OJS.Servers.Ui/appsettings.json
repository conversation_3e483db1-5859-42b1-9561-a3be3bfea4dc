{"ConnectionStrings": {"DefaultConnection": "Data Source=db; Initial Catalog=OpenJudgeSystem; User Id=sa; Password=********; TrustServerCertificate=True"}, "ApplicationSettings": {"SharedAuthCookieDomain": "localhost", "UiHomeYouTubeVideoId": "IwLET8SDBE4", "ApiKey": "12345", "OtlpCollectorBaseUrl": "http://alloy:4317/"}, "ApplicationUrls": {"AdministrationUrl": "http://localhost:5001", "SulsPlatformBaseUrl": "https://localhost:44304", "SulsPlatformApiKey": "1234", "FrontEndUrl": "http://localhost:5002"}, "Redis": {"ConnectionString": "redis,password=redisPass123,ConnectTimeout=10000,abortConnect=false", "InstanceName": "OJS"}, "MessageQueue": {"Host": "mq", "VirtualHost": "ojs", "User": "ojsuser", "Password": "myS3cretPass2"}, "CircuitBreakerResilienceStrategy": {"FailureRatio": 0.5, "MinimumThroughput": 10, "SamplingDuration": "00:00:30", "DurationOfBreak": "00:01:00"}, "Mentor": {"ApiKey": "MyS3cretApiKey"}, "Svn": {"BaseUrl": "MyS3cretSvnBaseUrl", "Username": "MyS3cretSvnUsername", "Password": "MyS3cretSvnPassword"}, "RetryResilienceStrategy": {"MaxRetryAttempts": 3, "Delay": "00:00:01", "BackoffType": "Exponential", "UseJitter": true}, "Emails": {"ServerHost": "YOUR_HOST", "ServerPort": "587", "ServerUsername": "YOUR_Username", "ServerPassword": "YOUR_Password", "SenderEmail": "<EMAIL>", "SenderDisplayValue": "YOUR_DISPLAY_NAME", "DevEmail": "<EMAIL>"}, "HealthCheck": {"Key": "Key", "Password": "password"}, "Logging": {"LogLevel": {"Default": "Error", "Microsoft": "Warning"}, "Debug": {"LogLevel": {"Default": "Information", "Microsoft.Hosting": "Trace"}}, "EventSource": {"LogLevel": {"Default": "Warning"}}}, "Http": {"MaxRequestSizeLimit": 35000000}, "MigrationsDbTimeoutInSeconds": 120, "AllowedHosts": "*"}