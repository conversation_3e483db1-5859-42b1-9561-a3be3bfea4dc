{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:36663", "sslPort": 44332}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "OJS.Servers.Ui": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": false, "applicationUrl": "http://localhost:5010", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://:18889", "OTEL_RESOURCE_ATTRIBUTES": "service.name=ui-api, deployment.environment=local"}}}}